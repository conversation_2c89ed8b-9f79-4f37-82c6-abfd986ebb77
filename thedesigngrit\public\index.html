<!DOCTYPE html>
<html lang="en">
  <script type="text/javascript" src="https://cdn.weglot.com/weglot.min.js"></script>
<script>
    Weglot.initialize({
        api_key: 'wg_0145d3c5c629871ba1c9b5a4468b9f784'
    });
</script>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="google-site-verification"
      content="MxZUsWMbEOJNGRT6hcm5ZADa3cEjShpVF0Xgjm2NDuo"
    />
    <meta
      name="description"
      content="The Design Grit is built on a foundation of timelessness, authority, and
      creative mastery. Focused on legacy, ownership, and bold innovation, the
      brand offers a curated, end-to-end interior design service. It helps users
      furnish their homes by selecting products from a diverse range of local brands,
      tailored to their personal style and needs."
    />
    <link rel="preload" href="/static/js/Home.chunk.js" as="script" />
    <link rel="preload" href="/static/js/Packages.chunk.js" as="script" />

    <link
      rel="preload"
      as="image"
      href="/Assets/Video-hero/poster.webp"
      type="image/webp"
    />
    <link
      rel="preload"
      as="video"
      href="/Assets/TDGLoadingScreen.webm"
      type="video/webm"
    />
    <link rel="preconnect" href="https://api.thedesigngrit.com" />
    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://use.fontawesome.com" crossorigin />
    <link
      rel="preconnect"
      href="https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev"
      crossorigin
    />

    <!-- Preload Fonts -->
    <link
      rel="preload"
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap"
      as="style"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap"
    />

    <!-- Font Awesome -->
    <link
      rel="preload"
      href="https://use.fontawesome.com/releases/v5.6.1/css/all.css"
      as="style"
    />
    <link
      rel="stylesheet"
      href="https://use.fontawesome.com/releases/v5.6.1/css/all.css"
    />

    <!-- Preload Critical CSS -->
    <link
      rel="preload"
      href="%PUBLIC_URL%/Styles/style.css?v=1.0"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript
      ><link rel="stylesheet" href="%PUBLIC_URL%/Styles/style.css?v=1.0"
    /></noscript>

    <!-- Load Other CSS Asynchronously -->
    <link
      rel="preload"
      href="%PUBLIC_URL%/Styles/responsive.css?v=1.0"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript
      ><link rel="stylesheet" href="%PUBLIC_URL%/Styles/responsive.css?v=1.0"
    /></noscript>

    <link
      rel="preload"
      href="%PUBLIC_URL%/Styles/VendorPanel.css?v=1.0"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript
      ><link rel="stylesheet" href="%PUBLIC_URL%/Styles/VendorPanel.css?v=1.0"
    /></noscript>

    <link
      rel="preload"
      href="%PUBLIC_URL%/Styles/vendor.css?v=1.0"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript
      ><link rel="stylesheet" href="%PUBLIC_URL%/Styles/vendor.css?v=1.0"
    /></noscript>

    <!-- Preload Logo for Faster LCP -->
    <!-- Preload Critical Hero Poster -->
    <!-- Preload videos -->
    <link
      rel="preload"
      as="video"
      href="/Assets/Video-hero/herovideo4.webm"
      type="video/webm"
    />
    <link
      rel="preload"
      as="video"
      href="/Assets/Video-hero/herovideo2.webm"
      type="video/webm"
    />
    <link
      rel="preload"
      as="video"
      href="/Assets/Video-hero/herovideo5.webm"
      type="video/webm"
    />
    <link
      rel="preload"
      as="video"
      href="/Assets/Video-hero/herovideo2.mp4"
      type="video/mp4"
    />
    <link
      rel="preload"
      as="video"
      href="/Assets/Video-hero/herovideo5.mp4"
      type="video/mp4"
    />
    <link
      rel="preload"
      as="video"
      href="/Assets/Video-hero/herovideo4.mp4"
      type="video/mp4"
    />

    <!-- Preload poster image -->
    <link
      rel="preload"
      as="image"
      href="/Assets/Video-hero/poster.webp"
      type="image/webp"
    />
    <link
      rel="preload"
      as="image"
      href="Assets/susSection-768.webp"
      type="image/webp"
    />

    <link
      rel="preload"
      as="image"
      href="/Assets/Video-hero/poster.avif"
      fetchpriority="high"
    />
    <script src="some-non-critical.js" defer></script>

    <!-- <link rel="preload" href="/Assets/TDG_Logo_Black.webp" as="image" /> -->
    <!-- App Metadata -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo.ico" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link rel="icon" href="%PUBLIC_URL%/logo.ico" />

    <title>The Design Grit</title>
    <!-- Error handling for Safari -->
    <script>
      window.addEventListener("error", function (e) {
        console.error("Global error:", e.error);
        // Track errors for debugging
        if (window.dataLayer) {
          window.dataLayer.push({
            event: "js_error",
            error_message: e.error?.message || "Unknown error",
          });
        }
      });

      // Handle unhandled promise rejections (common in Safari)
      window.addEventListener("unhandledrejection", function (e) {
        console.error("Unhandled promise rejection:", e.reason);
        e.preventDefault();
      });

      // Safari service worker fix
      if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.addEventListener(
          "controllerchange",
          function () {
            window.location.reload();
          }
        );
      }
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!-- Reduced video preloads - load on demand instead -->
    <script>
      // Preload videos only when needed and connection is good
      if (
        navigator.connection &&
        navigator.connection.effectiveType !== "slow-2g"
      ) {
        const videos = [
          "/Assets/Video-hero/herovideo4.webm",
          "/Assets/Video-hero/herovideo2.webm",
          "/Assets/TDGLoadingScreen.webm",
        ];

        videos.forEach((src) => {
          const link = document.createElement("link");
          link.rel = "preload";
          link.as = "video";
          link.href = src;
          document.head.appendChild(link);
        });
      }
    </script>
    <script src="/static/js/main.chunk.js" crossorigin="anonymous"></script>
  </body>
</html>
