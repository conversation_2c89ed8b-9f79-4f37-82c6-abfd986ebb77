.vendor-signin-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f4f4f4;
}

.vendor-signin-form {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 300px;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
}

.vendor-form-group {
  margin-bottom: 15px;
}

.vendor-form-group label {
  display: block;
  font-weight: bold;
}

.vendor-form-group input {
  width: 100%;
  padding: 8px;
  margin-top: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.vendor-signin-button {
  width: 100%;
  padding: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.vendor-signin-button:hover {
  background-color: #0056b3;
}

.vendor-error-message {
  color: red;
  text-align: center;
  margin-top: 10px;
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: transparent;
  padding: 20px;
  border-radius: 8px;
  width: 60%;
  height: 100%;
}

.modal-content h3 {
  margin-top: 0;
}

.modal-content form {
  display: flex;
  flex-direction: column;
}

.modal-content form input,
.modal-content form select,
.modal-content form button {
  margin: 10px 0;
}
.promotion-save-button,
.promotion-cancel-button {
  background-color: #6c7c59;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}
.promotion-cancel-button {
  background-color: #6c7c59;
  color: #2d2d2d;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}
.promotion-save-button:hover {
  background-color: #2d2d2d;
}

.promotion-cancel-button {
  background-color: transparent;
  border: 1px solid #6c7c59;
}

.promotion-cancel-button:hover {
  background-color: #2d2d2d;
  color: #f4f4f4;
}
