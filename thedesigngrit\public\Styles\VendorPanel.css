/* Navbar Styles */
.navbar-vendor {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f8f8;
  padding: 12px 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.navbar-logo-vendor img {
  height: 40px;
  align-items: center;
}

.navbar-actions-vendor {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}
.icon-vendor-bar {
  font-size: 1.2rem;
  color: #000000;
  cursor: pointer;
}
.navbar-search-vendor {
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-right: 10px;
}

.navbar-admin-button-vendor {
  padding: 5px 10px;
  background-color: #6a8452;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
.navbar-actions-vendor select {
  padding: 5px;
  border: 2px solid #ddd;
  border-radius: 5px;
  cursor: pointer;
}
/* Sidebar Styles */
.sidebar-vendor {
  font-family: Montserrat;
  width: 250px;
  background-color: #f8f8f8;
  border-right: 1px solid #ddd;
  padding: 20px;
}

.sidebar-menu-vendor {
  list-style: none;
  padding: 0;
  display: flex;
  gap: 20px;
  flex-direction: column;
}

.sidebar-item-vendor {
  font-size: 15px;
  padding: 10px 15px;
  cursor: pointer;
  border-radius: 6px;
  gap: 10px;
  transition: background-color 0.3s;
}

.sidebar-item-vendor:hover {
  padding: 10px 15px;
  cursor: pointer;
  background-color: #6a8452;
  border-radius: 6px;
  color: #fff;
}

.sidebar-item-vendor.active {
  background-color: #6a8452;
  border-radius: 6px;
  color: #fff;
}

.sidebar-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-item-icon {
  font-size: 20px;
  min-width: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-item-text {
  font-size: 14px;
  font-family: Montserrat, sans-serif;
  display: flex;
  margin-left: 5px;
  align-items: center;
}

/* Content Styles */
.main-content-vendor {
  display: flex;
}

.content-vendor {
  flex: 1;
  padding: 45px;
  background-color: #efebe8;
}

.dashboard-vendor,
.all-products-vendor,
.order-list-vendor {
  padding: 20px;
  border-radius: 8px;
}
/*Dashboard*/
.dashboard-vendor {
  padding: 20px;
  font-family: Montserrat;
}

.dashboard-header-vendor {
  display: flex;
  justify-content: space-between;
  align-items: self-start;
  margin-bottom: 20px;
}
.dashboard-header-title {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: baseline;
}
.dashboard-date-vendor {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}
.dashboard-date-vendor span {
  margin-right: 10px;
  font-size: 18px;
  align-items: flex-end;
}
.dashboard-overview-vendor {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card-vendor {
  flex: 1;
  padding: 20px;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  align-items: center;
  gap: 15px;
}

.card-icon-vendor {
  font-size: 30px;
  color: #6a8452;
}

.card-content-vendor h3 {
  font-size: 18px;
  margin: 0;
}

.card-content-vendor p {
  font-size: 20px;
  font-weight: normal;
  margin: 5px 0;
}

.card-content-vendor span {
  font-size: 14px;
  color: #6a8452;
}

.dashboard-chart-vendor {
  margin-bottom: 30px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 20px;
  width: auto;
  align-items: center;
  height: 392px;
}

.chart-header-vendor {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 0px;
  padding: 0px 20px;
  background-color: #fff;
  border-radius: 15px;
  border: 2px solid #ddd;
  width: 70%;
  height: 395px;
}

.chart-header-title-vendor {
  font-size: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 5px;
  border-bottom: 2px solid #ddd;
  text-align: left;
}

.chart-tabs-vendor {
  display: flex;
  gap: 10px;
  padding: 10px;
}

.chart-tab-vendor {
  padding: 0px 10px;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  border-radius: 5px;
  color: #6a8452;
  cursor: pointer;
}

.chart-tab-vendor.active {
  background-color: #6a8452;
  color: #fff;
}
.chart-tab-vendor:hover {
  background-color: #6a8452;
  color: #fff;
}

.chart-placeholder-icon-vendor {
  font-size: 150px;
  color: #ddd;
  display: block;
  margin: 0 auto;
}
.dashboard-lists-vendor {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 20px;
  width: 100%;
}
.recent-orders-vendor {
  padding: 20px;
  font-family: Montserrat;
  background-color: #fff;
  border-radius: 15px;
  border: 2px solid #ddd;
  width: 100%;
}
.recent-orders-vendor table {
  background-color: #fff;
  width: 100%;
  border-collapse: collapse;
}

.recent-orders-vendor th,
.recent-orders-vendor td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.status-delivered {
  color: green;
}

.status-cancelled {
  color: red;
}

.status-shipping {
  color: orange;
}
.best-sellers-vendor {
  padding: 20px;
  font-family: Montserrat;
  background-color: #fff;
  border-radius: 15px;
  border: 2px solid #ddd;
  width: 30%;
  height: 392px;
}

.best-sellers-vendor ul {
  list-style: none;
  padding: 0;
  margin: auto;
}

.best-sellers-vendor li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 10px 0;
}
.best-sellers-vendor img {
  width: 50px;
  height: 50px;
  border-radius: 5px;
  border: 1px solid #ddd;
}
/*Notification overlay*/
.notification-overlay-vendor {
  position: relative;
}

.notification-icon-vendor {
  font-size: 24px;
  background: none;
  border: none;
  cursor: pointer;
}

.overlay-container-vendor {
  position: absolute;
  top: 50px;
  right: 0;
  width: 360px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.overlay-header-vendor {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ddd;
}

.overlay-header-vendor h3 {
  margin: 0;
  font-size: 18px;
}

.close-button-vendor {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
}

.overlay-body-vendor {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item-vendor {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ddd;
}

.notification-image-vendor {
  width: 40px;
  height: 40px;
  background: #eee;
  border-radius: 4px;
  margin-right: 16px;
}
.notification-image-vendor-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}
.notification-details-vendor {
  flex: 1;
  font-family: "Montserrat";
}

.notification-details-vendor h4 {
  margin: 0;
  font-size: 14px;
  font-family: "Horizon";
}

.notification-details-vendor p {
  margin: 4px 0;
  font-size: 14px;
  font-weight: bold;
  font-family: Montserrat;
}

.notification-details-vendor span {
  font-size: 12px;
  color: #888;
  font-family: Montserrat;
}

.notification-status-vendor {
  background: #6a8452;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: Montserrat;
}

.overlay-footer-vendor {
  align-items: center;
  display: flex;
  justify-content: center;
  padding: 16px;
  border-top: 1px solid #ddd;
}

.view-all-vendor {
  border: none;
  background: #6a8452;
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}
.mark-read-vendor {
  border: none;
  background: #2d2d2d;
  color: #fff;
  padding: 8px 10px;
  border-radius: 4px;
  cursor: pointer;
}
.mark-read-vendor:hover {
  background: #6a8452;
}
.view-all-vendor:hover {
  background: #2d2d2d;
}
/*Product list*/
/* styles.css */

.product-list-page-vendor {
  padding: 20px;
  font-family: Montserrat;
}

.dashboard-header-vendor {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-header-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.dashboard-header-title p {
  margin: 5px 0 0;
  font-size: 14px;
  color: #777;
}

.dashboard-date-vendor button {
  display: flex;
  align-items: center;
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dashboard-date-vendor button:hover {
  background-color: #45a049;
}

.vendor-products-list-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.product-card-request {
  display: flex;
  flex-direction: column;

  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card-request:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.product-grid {
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(250px, 1fr)
  ); /* Adjust the minmax value as needed */
  gap: 16px; /* Space between grid items */
}
.all-product-card {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.all-product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.menu-container {
  position: relative;
  display: inline-block;
}

.three-dots-icon {
  cursor: pointer;
  font-size: 20px;
  color: #555;
}

.menu-dropdown {
  position: absolute;
  right: 0;
  top: 25px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  width: 120px;
  display: flex;
  flex-direction: column;
}

.menu-dropdown button {
  background: none;
  border: none;
  padding: 8px 12px;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.menu-dropdown button:hover {
  background-color: #f5f5f5;
  color: #6e7e5f;
}
.product-card-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 100px;
  padding-top: 19px;
  margin-bottom: -10px;
}
.product-info-vendor {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 15px;
  flex-grow: 1;
}
.product-info-vendor h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.product-info-vendor p {
  font-size: 12px;
  margin: 5px 0 0;
  color: #666;
}
.all-product-image {
  width: 35%;
  height: 70%;
  object-fit: cover;
  border-radius: 4px;
  margin-left: 20px;
  margin-bottom: 20px;
}

.product-card-body {
  padding: 0px 15px 15px 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
}

.product-summary {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}

.product-stats {
  border: 1px solid #ddd;
  margin-top: 30px;
  padding: 10px;
  border-radius: 8px;
}

.product-sales,
.product-remaining {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 5px 0;
}

.sales-value,
.remaining-value {
  font-weight: bold;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.pagination button {
  margin: 5px;
  padding: 8px 12px;
  background-color: #f0f0f0;
  color: #000;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.pagination button.active {
  background-color: #2d2d2d;
  color: #fff;
  font-weight: bold;
}
@media (max-width: 768px) {
  .vendor-products-list-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}
/*Order List*/
.recent-purchases {
  font-family: Montserrat;
  margin: 20px;
  background-color: #fff;
  padding: 20px;
  border-radius: 15px;
}

h2 {
  margin-bottom: 10px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  border-bottom: 1px solid #ddd;
  text-align: left;
  padding: 12px;
}

tr:hover {
  background-color: #f9f9f9;
}

.status-delivered {
  color: green;
  font-weight: bold;
}

.status-canceled {
  color: orange;
  font-weight: bold;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.pagination button {
  margin: 5px;
  padding: 8px 12px;
  border: none;
  background-color: #eaeaea;
  cursor: pointer;
  border-radius: 5px;
}

.pagination button.active {
  background-color: #2d2d2d;
  color: white;
  font-weight: bold;
}

.pagination button:hover {
  background-color: #c5c5c5;
}

/*Table in order Details vendor*/
.products-purchases-order {
  font-family: Montserrat;
  background-color: #fff;
  padding: 20px;
  border-radius: 15px;
}

h2 {
  margin-bottom: 10px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  border-bottom: 1px solid #ddd;
  text-align: left;
  padding: 12px;
}

th {
  color: #ddd;
}

tr:hover {
  background-color: #f9f9f9;
}

.status-delivered {
  color: green;
  font-weight: bold;
}

.status-canceled {
  color: orange;
  font-weight: bold;
}

/*prouc d*/
.product-form {
  background-color: #fff;
  border-radius: 15px;
  display: flex;
  gap: 4rem;
  padding: 2rem;
  font-family: Montserrat;
}

.form-left {
  flex: 1;
}

.form-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  font-weight: bold;
  margin-bottom: 0.5rem;
}
.dropdown-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border: 2px solid #ccc;
  border-radius: 8px;
  padding: 20px;
}
input,
textarea {
  width: 100%;
  padding: 0.7rem;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 1rem;
}

textarea {
  height: 6rem;
}

.form-row {
  display: flex;
  gap: 3rem;
}

.half-width {
  flex: 1;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background-color: #2d2d2d;
  color: #fff;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  flex-direction: row;
  margin-top: 1rem;
}
.remove-tag-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 16px;
  margin-top: 0px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.remove-tag-btn:hover {
  background: none;
  border: none;
  color: #ff4d4d;
}
.image-placeholder {
  width: 100%;
  height: 200px;
  background-color: #ccc;
  border-radius: 5px;
}
.image-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-gallery {
  border-top: 1px solid #ccc;
  padding-top: 1rem;
}

.drop-zone {
  border: 2px dashed #ccc;
  text-align: center;
  padding: 1.5rem;
  color: #888;
  font-size: 0.9rem;
  justify-content: center;
}

.drop-zone input[type="file"] {
  display: flex;
  width: 95%;
  justify-content: center;
}
.upload-btn {
  margin-top: 1rem;
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  color: #fff;
  background-color: #6e7e5f;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.upload-btn:hover {
  background-color: #55664e;
}
.thumbnail-list {
  padding: 15px 0px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.thumbnail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.image-thumbnail {
  width: 50px;
  height: 50px;
  background-color: #ccc;
  border-radius: 5px;
}

.progress-bar {
  flex: 1;
  height: 5px;
  background-color: #ddd;
  position: relative;
}

.progress-bar::after {
  content: "";
  position: absolute;
  width: 70%;
  height: 100%;
  background-color: #6e7e5f;
}
/* Remove thumbnail button */
.remove-thumbnail {
  top: 0;
  right: 0;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  cursor: pointer;
  font-size: 12px;
}

.remove-thumbnail:hover {
  background-color: #c0392b;
}

.checkmark {
  color: #6e7e5f;
  font-size: 1.2rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.btn {
  padding: 0.7rem 1.5rem;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  border-radius: 5px;
}

.update {
  background-color: #333;
  color: #fff;
}

.delete {
  background-color: #6e7e5f;
  color: #fff;
}

.cancel {
  background-color: transparent;
  color: #333;
  border: 1px solid #ccc;
}
/*Admin Requests partners*/
.request-card-body {
  padding: 15px 15px 15px 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
  margin-top: 2rem;
}
.request-info-vendor {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 15px;
  flex-grow: 1;
}
.request-info-vendor h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.request-info-vendor p {
  font-size: 12px;
  margin: 5px 0 0;
  color: #666;
}
.request-image {
  width: 30%;
  height: auto;
  object-fit: cover;
}
.request-summary {
  font-size: 14px;
  color: #666;
  margin-top: 20px;
}
.verify-partners {
  font-family: Montserrat;
  margin: 20px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

h1 {
  text-align: center;
  color: #333;
}

.partner-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  width: 80%;

  align-items: center;
}

.partner-info {
  display: flex;
  gap: 20px;
  flex-direction: column;
  width: 80%;
  margin-top: 50px;
}

.partner-info img {
  align-items: center;
  max-width: 150px;
  border-radius: 8px;
  margin-top: 50px;
  margin: auto;
}

.partner-info div {
  flex: 1;
}

.partner-info h3,
.partner-info h4 {
  margin-bottom: 5px;
  color: #555;
}

.partner-info p {
  margin-bottom: 10px;
  color: #333;
}

ul {
  list-style-type: disc;
  margin-left: 20px;
}
li {
  font-size: large;
}
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.approve-btn,
.reject-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
}

.approve-btn {
  background-color: #d4edda;
  color: #155724;
}

.reject-btn {
  background-color: #f8d7da;
  color: #dc3545;
}

.approve-btn:hover {
  background-color: #155724;
  color: #fff;
}

.reject-btn:hover {
  background-color: #dc3545;
  color: #fff;
}
.brand-signup-form {
  width: 80%;
  margin: auto;
  padding: 20px;
  border-radius: 15px;
  align-items: center;
}
/* Add this to your CSS file */
.brand-signup-form .social-links {
  display: flex;
  flex-wrap: wrap;
  gap: 55px; /* Space between the items */
}

.brand-signup-form .social-link {
  width: 45%; /* Adjust this to control the width of each input */
}

.brand-signup-form .form-field {
  margin-bottom: 15px;
}

.brand-signup-form button {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: #6b7b58;
  color: white;
  border: none;
  cursor: pointer;
}

.brand-signup-form button:hover {
  background-color: #555;
}
/* Branding Page Form */
.branding-page-form {
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  margin: 0 auto;
}

.branding-page-form h2 {
  text-align: center;
  margin-bottom: 20px;
}

.branding-page-form .form-field {
  margin-bottom: 20px;

  align-items: center;
}

.branding-page-form .form-field label {
  font-weight: bold;
  width: 160px;
}

.branding-page-form .form-field input,
.branding-page-form .form-field textarea {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ccc;
}
.branding-page-form .form-field textarea {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
  border: 1px solid #ccc;
}
.branding-page-form .form-field textarea {
  height: 120px;
}

.branding-page-form .form-field img {
  max-width: 100px;
  max-height: 100px;
  margin-left: 10px;
}

.branding-page-form button {
  background-color: #6b7b58;
  color: white;
  border: none;
  cursor: pointer;
}

.branding-page-form button:hover {
  background-color: #555;
}
.color-options-checkbox {
  padding: 0px;
  display: flex;
  align-items: center;
  gap: 20px;
  flex-direction: row;
  justify-content: space-between;
}
.color-options-checkbox label {
  padding: 0px;
  width: 160px;
  font-weight: normal;
}
.color-options-checkbox input[type="checkbox"],
input[type="radio"] {
  padding: 0px;
  margin: 0;
}
.warranty-field select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #2d2d2d;
  border-radius: 6px;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}
/*Notification popup overlay details*/
.notifiy-overlay-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.notifiy-overlay-content {
  background: #6b7b58;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.notifiy-overlay-content h2 {
  font-family: Horizon;
  color: white;
}

.notifiy-overlay-content p {
  font-family: Montserrat;
  font-weight: normal;
  text-align: center;
  color: white;
}
.notifiy-overlay-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}
.notifiy-overlay-content button {
  background-color: #2d2d2d;
  color: white;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  transition: 0.3s;
}
.notifiy-overlay-content button:hover {
  background-color: #555;
}
.brand-signup-form {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.brand-header {
  text-align: center;
  margin-bottom: 40px;
}

.brand-header h2 {
  font-size: 32px;
  color: #333;
}

.brand-logo img {
  border-radius: 8px;
  max-width: 150px;
  margin: 10px 0;
}

.brand-info {
  margin-bottom: 20px;
}

.form-field {
  margin-bottom: 20px;
}

.form-field label {
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
  color: #555;
}

.form-field input[type="text"],
.form-field input[type="email"],
.form-field input[type="file"] {
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ddd;
  font-size: 14px;
  background-color: #f9f9f9;
}

.form-field input[type="file"] {
  border: none;
}

.display-content {
  font-size: 14px;
  color: #555;
}

.display-content a {
  color: #007bff;
  text-decoration: none;
}

.display-content a:hover {
  text-decoration: underline;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-save,
.btn-cancel,
.btn-edit {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-save {
  background-color: #28a745;
  color: white;
  border: none;
}

.btn-cancel {
  background-color: #dc3545;
  color: white;
  border: none;
}

.btn-edit {
  background-color: #007bff;
  color: white;
  border: none;
}

.btn-save:hover {
  background-color: #218838;
}

.btn-cancel:hover {
  background-color: #c82333;
}

.btn-edit:hover {
  background-color: #0056b3;
}
/* Quotations Page */
.quotations-page {
  padding: 20px;
}

h1 {
  text-align: left;
  font-family: Montserrat;
  margin-bottom: 20px;
}

/* List of Quotations */
.quotations-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: start;
}

.quotation-card {
  width: 250px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.quotation-card:hover {
  transform: scale(1.05);
}

.quotation-card-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.quotation-card-info {
  padding: 10px;
  text-align: center;
  margin-top: -4px;
  height: 120px;
  background-color: #f8f9fa;
}

.quotation-card-info h2 {
  font-size: 20px;
  color: #333;
}

/* Popup */
.quotation-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.quotation-popup-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
  position: relative;
  overflow-y: auto;
  max-height: 80vh;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 30px;
  cursor: pointer;
}

.quotation-popup-img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  margin-bottom: 20px;
}

.quotation-popup p {
  font-size: 1.1em;
  margin-bottom: 10px;
}
.no-quotations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh; /* Adjust as needed */
  font-size: 1.5rem;
  font-weight: bold;
  color: #ccc; /* Warning color */
}

.exclamation {
  font-size: 3rem;
  margin-top: 10px;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px; /* Adjust the width of the scrollbar */
  height: 10px; /* For horizontal scrollbar */
}

::-webkit-scrollbar-thumb {
  background-color: #6b7b58; /* Sage green color */
  border-radius: 10px; /* Rounded corners */
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1; /* Light background for the track */
  border-radius: 10px; /* Rounded corners for the track */
}

::-webkit-scrollbar-thumb:hover {
  background-color: #495a3a; /* Darker sage green when hovering */
}
.popup-overlay-employee {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-content-employee {
  background: white;
  padding: 20px;
  border-radius: 8px;
  position: relative;
  max-width: 500px;
  width: 100%;
  height: 650px;
}
.product-metrics ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-top: 5px;
}
/* Variant Modal Styles
.variant-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.variant-modal-container {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
}

.variant-form {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.variant-images {
  grid-column: span 2;
}

.variant-image-previews {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.variant-thumbnail {
  position: relative;
  width: 80px;
  height: 80px;
}

.variant-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.variant-thumbnail button {
  position: absolute;
  top: 0;
  right: 0;
  background: red;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.variant-list {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.variant-list ul {
  list-style: none;
  padding: 0;
}

.variant-list li {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
}

.add-variant-btn {
  margin-top: 10px;
  background-color: #4caf50;
  color: white;
}

.variant-image-upload {
  margin-top: 20px;
  padding: 15px;
  border: 1px dashed #ccc;
  border-radius: 5px;
}

.upload-label {
  display: inline-block;
  padding: 8px 15px;
  background-color: #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 15px;
  transition: background-color 0.2s;
}

.upload-label:hover {
  background-color: #e0e0e0;
}

.variant-image-previews {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.variant-thumbnail {
  position: relative;
  width: 80px;
  height: 80px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: border-color 0.2s;
}

.variant-thumbnail:hover {
  border-color: #ddd;
}

.variant-thumbnail.main-thumbnail {
  border-color: #4caf50;
}

.variant-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-image-badge {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(76, 175, 80, 0.8);
  color: white;
  text-align: center;
  font-size: 12px;
  padding: 2px 0;
}

.main-image-selection {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.main-image-selection p {
  margin-bottom: 10px;
  font-weight: bold;
}

.main-image-preview {
  max-width: 200px;
  max-height: 200px;
  border: 2px solid #4caf50;
  border-radius: 4px;
} */
.promotions-page-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.promotions-content {
  margin-top: 40px;
}

.promotions-header {
  text-align: center;
  margin-bottom: 40px;
}

.promotions-header h1 {
  font-size: 32px;
  margin-bottom: 10px;
}

.promotions-header p {
  color: #666;
  font-size: 16px;
}

.promotion-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.section-header:hover {
  background-color: #f0f0f0;
}

.section-header h2 {
  margin: 0;
  font-size: 20px;
}

.toggle-icon {
  font-size: 20px;
}

.promotion-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.promotion-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  background-color: white;
}

.promotion-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.promotion-image-container {
  position: relative;
  height: 200px;
}

.promotion-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.discount-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #e74c3c;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: bold;
}

.promotion-details {
  padding: 15px;
}

.promotion-details h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
}

.brand-name {
  color: #666;
  margin: 0 0 10px 0;
  font-size: 14px;
}

.price-container {
  display: flex;
  align-items: flex-start;
  flex-direction: row;
  gap: 10px;
  margin-bottom: 10px;
}

.original-price {
  text-decoration: line-through;
  color: #999;
}

.sale-price {
  font-weight: bold;
  color: #e74c3c;
  font-size: 18px;
}

.promotion-dates {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}

.metrics-container {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.metric-value {
  font-weight: bold;
  font-size: 16px;
  padding: 4px 0px;
}

.no-promotions {
  grid-column: 1 / -1;
  text-align: center;
  padding: 30px;
  color: #666;
}

@media (max-width: 768px) {
  .promotion-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }

  .promotions-header h1 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .promotion-grid {
    grid-template-columns: 1fr;
  }
}
.brand-status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-active {
  background-color: #e6f7ee;
  color: #0d9f61;
}

.status-pending {
  background-color: #fff8e6;
  color: #f5a623;
}

.brand-card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.brand-description {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.social-icon {
  transition: transform 0.2s;
}

.social-icon:hover {
  transform: scale(1.2);
}

.brand-detail-row {
  margin-bottom: 8px;
}

.brand-detail-label {
  font-weight: 600;
  color: #666;
}

.brand-detail-value {
  word-break: break-word;
}

.brand-logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 140px;
  background-color: #f5f5f5;
  padding: 16px;
}

.brand-logo-image {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}
/* Vendor Types Swiper Styles */
.vendor-types-swiper {
  width: 100%;
  margin: 0 auto;
}

.vendor-types-swiper .swiper-slide {
  height: auto;
  transition: transform 0.3s ease;
}

.vendor-types-swiper .swiper-pagination {
  bottom: 0;
}

.vendor-types-swiper .swiper-pagination-bullet {
  background: #6b7b58;
  opacity: 0.5;
}

.vendor-types-swiper .swiper-pagination-bullet-active {
  background: #6b7b58;
  opacity: 1;
}

@media screen and (max-width: 768px) {
  .vendor-types-swiper {
    padding-bottom: 30px;
  }
}
.modal-overlay-uploadimage {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(10px); /* Adds blur to the background */
}

.modal-content-uploadimage {
  width: 70vw;
  height: 70vh;
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cropper-container-uploadimage {
  position: relative;
  width: 100%;
  height: calc(70vh - 100px); /* reserve space for buttons */
}

.cropper-buttons-uploadimage {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}
.profile-card-flip-container {
  perspective: 1200px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  z-index: 2000;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.profile-card-flip {
  width: 350px;
  height: 400px;
  transition: transform 0.6s cubic-bezier(0.4, 2.08, 0.55, 0.44);
  transform-style: preserve-3d;
  position: relative;
  animation: flipIn 0.7s cubic-bezier(0.4, 2.08, 0.55, 0.44);
}

@keyframes flipIn {
  from {
    transform: rotateY(-90deg);
    opacity: 0;
  }
  to {
    transform: rotateY(0deg);
    opacity: 1;
  }
}

.profile-card-front {
  background: #6b7b58;
  color: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  padding: 32px 24px;
  width: 100%;
  height: 100%;
  font-family: Montserrat, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.profile-card-front h2 {
  font-family: Horizon, Montserrat, sans-serif;
  margin-bottom: 18px;
  font-size: 1.6rem;
  letter-spacing: 1px;
}

.profile-card-content p {
  margin: 8px 0;
  font-size: 1.1rem;
  text-align: left;
  width: 100%;
}

.profile-card-close-btn {
  margin-top: 24px;
  background: #fff;
  color: #6b7b58;
  border: none;
  border-radius: 8px;
  padding: 10px 32px;
  font-size: 1rem;
  cursor: pointer;
  font-family: Montserrat;
  font-weight: bold;
  transition: background 0.2s, color 0.2s;
}
.profile-card-close-btn:hover {
  background: #2d2d2d;
  color: #fff;
}
