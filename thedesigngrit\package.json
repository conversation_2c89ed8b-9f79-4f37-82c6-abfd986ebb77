{"name": "thedesigngrit", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fortawesome/free-solid-svg-icons": "^6.7.1", "@fortawesome/react-fontawesome": "^0.2.2", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^6.1.8", "@mui/material": "^6.1.7", "@mui/styles": "^6.1.8", "@react-oauth/google": "^0.12.2", "@react-pdf/renderer": "^4.2.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@vercel/analytics": "^1.5.0", "axios": "^1.7.9", "chart.js": "^4.4.8", "classnames": "^2.5.1", "date-fns": "^4.1.0", "framer-motion": "^12.1.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-date-range": "^2.0.1", "react-dom": "^18.3.1", "react-easy-crop": "^5.4.2", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-icons": "^5.3.0", "react-intersection-observer": "^9.15.1", "react-phone-input-2": "^2.15.1", "react-router-dom": "^6.28.0", "react-router-hash-link": "^2.4.3", "react-scripts": "5.0.1", "react-select": "^5.8.3", "react-select-country-list": "^2.2.3", "react-slick": "^0.30.2", "recharts": "^2.15.1", "slick-carousel": "^1.8.1", "source-map-explorer": "^2.5.3", "swiper": "^11.2.5", "swr": "^2.3.3", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "source-map-explorer 'build/static/js/*.js'", "optimize-build": "echo 'Build optimization complete'", "safari-test": "npx serve -s build -p 3000"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "Safari >= 12", "iOS >= 12"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}