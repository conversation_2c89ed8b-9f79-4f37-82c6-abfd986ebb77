@font-face {
  font-family: "Horizon";
  src: url("../fonts/horizon/horizon.otf") format("opentype");
  font-weight: normal;
  font-style: normal;
}
/* Mobile (up to 767px) */
@media (max-width: 767px) {
  body {
    font-size: 14px;
  }

  .container {
    flex-direction: column;
    height: 100%;
  }

  .container-login {
    padding-left: 20px;
    padding-right: 20px;
    background-size: cover;
  }

  .login-form {
    width: 90%;
    height: 66%;
  }
  .signup-form-container {
    width: 95%;
    height: 72%;
  }

  .search-bar,
  .icon-container,
  .header-bottom,
  .moodboard-btn {
    display: none; /* Hide elements that are unnecessary on mobile */
  }

  .hamburger {
    display: block; /* Show hamburger on mobile */
    width: 80px;
    height: 30px;
  }

  .logo img {
    width: 95px;
    height: 45px;
    z-index: 2;
  }
  .content-container {
    padding: 5% 5%;
    flex-direction: column;
  }

  .Contant-caption h3 {
    font-size: 1.8rem;
    text-align: center;
  }

  .Contant-caption p {
    font-size: 1rem;
  }

  .contact-form-field input,
  .contact-form-field textarea {
    font-size: 13px;
  }
  .header-top {
    padding: 10px 15px;
    margin-bottom: -3rem;
  }
  .header-bottom {
    display: none;
  }

  .types-container {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* Tablets (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1199px) {
  body {
    font-size: 16px;
  }

  .container-login {
    flex-direction: column;
    height: 100vh;
    padding: 30px;
  }

  .login-form {
    width: 70%;
    height: 520px;
    padding: 30px;
  }
  .signup-form-container {
    width: 70%;
    height: auto;
    padding: 30px;
  }

  .hero-home-section {
    height: 400px;
  }

  .hero-text-overlay h1 {
    font-size: 2.5rem;
  }

  .header-top {
    flex-direction: row;
    justify-content: space-between;
  }

  .header-bottom {
    gap: 20px;
  }

  .category-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .team-member {
    width: 48%;
  }

  .footer-container {
    text-align: left;
    padding: 40px;
  }
  .content-container {
    padding: 5% 5%;
  }

  .Contant-caption h3 {
    font-size: 1.8rem;
  }

  .Contant-caption p {
    font-size: 1rem;
  }
  .types-container {
    grid-template-columns: repeat(3, 1fr);
  }
  .menu-overlay {
    width: 80%;
  }
  .concept-explore-container {
    margin-top: 26rem;
  }
  .concept-card {
    height: 26vh;
  }
  .right-card {
    height: 29vh;
  }
}

/* Medium Laptop (1024px - 1440px) */
@media (min-width: 1024px) and (max-width: 1440px) {
  .signup-form-container {
    width: 90%;
    max-width: 400px;
    margin: 0 auto;
    padding: 50px;
  }

  .signup-form {
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
  }

  .signinLogo img {
    width: 100px;
    height: 50px;
    margin: auto;
  }

  .form-title-signup {
    font-size: 1.4rem;
    text-align: center;
  }

  .input-field {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
  }

  .error-message {
    font-size: 0.8rem;
    margin-top: -10px;
    margin-bottom: 10px;
  }

  .password-strength-container {
    margin-bottom: 15px;
  }

  .terms-container {
    margin-bottom: 20px;
  }

  .signup-button {
    width: 100%;
    padding: 12px;
  }
}
/* General styles are unchanged from the original */

/* Media Queries for Responsive Design */
@media (max-width: 1024px) {
  /* For tablets and smaller laptops */
  .hero-container {
    flex-direction: row;
    padding: 20px;
    height: auto;
  }

  .hero-text {
    padding-left: 20px;
    max-width: 100%;
    text-align: left; /* Center text for smaller screens */
    padding-bottom: 20px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 14px;
  }

  .hero-section-image img {
    height: 209px; /* Maintain aspect ratio */
    border-radius: 20px;
    margin-left: 150px;
    margin-right: 0px;
    width: 238px;
  }

  .Caption-AboutUs {
    padding: 50px 20px;
    font-size: 18px;
  }

  .ourMission-Section {
    flex-direction: row; /* Stack image and text */
    padding: 40px;
  }

  .ourMission-Section-image img {
    width: 50%;
  }

  .ourMission-Section-typo {
    padding-left: 0;
    margin-top: 20px;
    padding: 10px;
    text-align: justify; /* Center text for smaller screens */
  }

  .ourStory-Section-typo {
    padding-left: 20px;
    padding-right: 20px;
    margin-bottom: 40px;
    text-align: justify;
  }
  .ourStory-Section-typo h2 {
    font-size: 18px;
    letter-spacing: 1.5px;
  }
  .OurTeam-section p {
    padding: 10px 20px;
    font-size: 14px;
  }

  .team-members {
    flex-direction: row;
    gap: 10px;
  }

  .team-member {
    width: 90%; /* Reduce width for smaller screens */
    margin: 0 auto;
  }
  .concept-title-container {
    margin-top: 50px;
  }

  .concept-card {
    height: 35vh;
  }
  .middle-card {
    width: 450px;
  }
  .left-card {
    height: 30vh;
  }

  .right-card {
    height: 30vh;
  }
  .product-grid-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 767px) {
  /* For mobile devices */
  .hero-container {
    padding: 15px;
    flex-direction: column;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 12px;
  }
  .hero-section-image {
    flex: 1;
    max-width: 100%;
    object-fit: cover; /* Adjust as needed */
    padding-right: 0px;
  }
  .hero-section-image img {
    height: 209px; /* Maintain aspect ratio */
    border-radius: 20px;
    margin-left: 0px;
    margin-right: 0px;
    width: 238px;
  }
  .Caption-AboutUs {
    font-size: 16px;
  }

  .ourMission-Section {
    padding: 20px;
    flex-direction: column;
  }

  .ourMission-Section-image img {
    width: 95%;
  }

  .ourMission-Section-typo {
    padding-left: 0;
    margin-top: 10px;
  }
  .ourMission-Section-typo h2 {
    font-size: 18px;
    letter-spacing: 1.5px;
  }
  .line-between {
    margin: 0 auto;
  }
  .OurTeam-section {
    padding-top: 20px;
  }
  .OurTeam-section p {
    font-size: 12px;
    padding: 10px;
  }

  .team-member {
    width: 100%;
    padding: 10px;
  }
  .team-members {
    flex-direction: column;
  }
  .team-member-image {
    width: 100px; /* Smaller image size for mobile */
    height: 100px;
  }

  .team-member-name {
    font-size: 1.2rem;
  }

  .team-member-title,
  .team-member-subtitle {
    font-size: 0.9rem;
  }

  .linkedin-icon {
    font-size: 1.2rem;
  }
  .grid-container {
    padding: 120px 20px 120px 20px;
    gap: 110px;
  }
  .page-container {
    flex-direction: column;
  }
  .collapsible-container {
    width: 100%;
    margin-left: -100px;
  }
  .right-side-content {
    width: 100%;
    margin-left: 10px;
  }

  .reviews-section {
    width: 100%;
    padding-left: 1rem;
  }
  .product-card {
    flex: 0 0 50%;
  }
}
/*careers responsive*/
/* General styles are unchanged from the original */

/* Media Queries for Responsive Design */
@media (max-width: 1024px) and (min-width: 768px) {
  /* For tablets and small-screen laptops */
  .values-grid {
    grid-template-columns: repeat(2, 1fr); /* Two columns instead of three */
    gap: 15px; /* Adjust spacing between grid items */
    width: 100%; /* Reduce grid width */
  }

  .value-card {
    flex-direction: row; /* Stack content vertically */
    padding: 15px;
    justify-content: space-between;
    margin: 0;
  }

  .value-card-icon {
    margin-bottom: 20px;
    font-size: 30px; /* Adjust icon size */
  }

  .value-card-title {
    font-size: 16px;
  }

  .value-card-description {
    font-size: 11px;
    padding-right: 20px;
  }
  .ourCoreValues-section {
    padding: 0 50px;
  }
  .career-container {
    max-width: 90%; /* Reduce container width */
    padding: 15px;
  }

  .career-header {
    font-size: 22px;
  }

  .tab {
    font-size: 12px; /* Reduce font size for tabs */
    padding: 8px 15px; /* Adjust padding */
  }

  .job-card {
    padding: 10px 15px; /* Reduce padding */
  }

  .job-title {
    font-size: 16px;
  }

  .job-description {
    font-size: 13px;
  }
  .grid-container {
    padding: 120px 20px 120px 20px;
    gap: 110px;
  }
  .page-container {
    flex-direction: row;
  }
  .collapsible-container {
    width: 100%;
    margin-left: -100px;
  }
  .right-side-content {
    width: 100%;
    margin-left: 10px;
  }

  .reviews-section {
    width: 100%;
    padding-left: 1rem;
  }
  .slider-content {
    gap: 15px;
  }

  .product-card {
    min-width: 300px;
  }

  .product-title-bestseller {
    font-size: 16px;
  }

  .product-price-bestseller {
    font-size: 14px;
  }
}

@media (max-width: 760px) and (min-width: 320px) {
  .grid-container {
    padding: 120px 20px 120px 20px;
    gap: 110px;
  }
  .page-container {
    flex-direction: column;
  }
  .collapsible-container {
    width: 100%;
    margin-left: -100px;
  }
  .right-side-content {
    width: 100%;
    margin-left: 10px;
  }
  .reviews-section {
    width: 100%;
    padding-left: 1rem;
  }
  /* For mobile devices */
  .values-grid {
    grid-template-columns: 1fr; /* One column layout */

    gap: 15px;
    width: 90%; /* Full width for mobile */
    height: 150vh;
  }

  .value-card {
    border: 2px solid #cecccc;
    padding: 10px;
    box-shadow: none; /* Remove shadow for simplicity */
    flex-direction: row;
    box-shadow: 3px 6px 6px 6px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .value-card-icon {
    font-size: 32px;
    padding-right: 10px;
  }

  .value-card-title {
    font-size: 20px;
  }

  .value-card-description {
    font-size: 16px;
    padding-right: 10px;
  }
  .ourCoreValues-section {
    padding: 0px 30px;
  }
  .career-container {
    padding: 10px;
    width: 80%;
  }

  .career-header {
    font-size: 20px;
  }

  .tab {
    font-size: 10px;
    padding: 6px 10px;
  }

  .job-card {
    padding: 8px 10px;
  }

  .job-title {
    font-size: 14px;
  }

  .job-description {
    font-size: 12px;
  }

  .job-tag {
    font-size: 10px; /* Reduce tag size for mobile */
    padding: 3px 8px;
  }
  .grid-container {
    padding: 120px 20px 120px 20px;
    gap: 110px;
  }
  .page-container {
    flex-direction: column;
  }
  .collapsible-container {
    width: 100%;
    margin-left: -100px;
  }
  .right-side-content {
    width: 100%;
    margin-left: 10px;
  }
  .concept-title {
    padding: 20px;
    width: 100%;
  }
}
/*Job Description*/
/* General styles are unchanged from the original */

/* Media Queries for Responsive Design */
@media (max-width: 1024px) {
  /* For tablets and small-screen laptops */
  .Description {
    padding: 30px;
  }

  .description-title h2 {
    font-size: 22px; /* Adjust title size */
  }

  .description-title p {
    font-size: 16px; /* Adjust subtitle size */
  }

  .description-content-1 {
    padding: 15px 20px; /* Reduce padding */
  }

  .description-content-1 h2 {
    padding-left: 0; /* Remove left padding */
    font-size: 20px; /* Adjust heading size */
  }

  .description-content-1 p {
    font-size: 15px; /* Adjust paragraph font size */
    padding-left: 0; /* Remove left padding */
    padding-right: 0; /* Remove right padding */
  }

  ul {
    padding-left: 20px; /* Add some padding for list items */
  }

  li {
    font-size: 15px; /* Adjust list item font size */
    line-height: 1.6;
  }
  .forgot-password-link {
    right: 112px;
  }
}

@media (max-width: 768px) {
  /* For mobile screens */
  /* .hero-job-container {
    flex-direction: row; 
    height: auto; 
    padding: 30px;
    text-align: center; 
  }
  .hero-text h1 {
    font-size: 20px; 
    text-align: left;
  }

  .hero-text p {
    font-size: 14px;
  } */

  .Description {
    padding: 20px;
  }

  .description-title h2 {
    font-size: 22px; /* Smaller title size */
  }

  .description-title p {
    font-size: 14px; /* Smaller subtitle size */
  }

  .description-content-1 {
    padding: 10px 15px; /* Compact padding */
  }

  .description-content-1 h2 {
    font-size: 18px; /* Smaller heading size */
  }

  .description-content-1 p {
    font-size: 14px; /* Smaller paragraph font size */
  }

  ul {
    padding-left: 15px; /* Adjust list padding */
  }

  li {
    font-size: 14px; /* Smaller font size for list items */
  }
  .shop-button {
    width: 43%;
  }
  .MuiPaper-root {
    width: 100%;
    padding: 0px;
  }

  .product-image-home img {
    width: 100%;
    height: 100%;
  }

  .MuiDialogContent-root {
    padding: 16px;
  }

  .product-card {
    flex: 0 0 100%;
  }
  .subcategory-box,
  .subcategory-box.reverse {
    flex-direction: column;
    width: 100%;
  }
  .subcategory-content {
    align-items: center;
  }
  .subcategory-image {
    width: 100%;
  }
  .reviews-header {
    flex-direction: column;
    padding-left: 10px;
  }
  .forgot-password-link {
    right: 68px;
  }
  .subcategory-content {
    width: 100%;
    padding: 30px;
  }
  .subcategory-content h2 {
    font-size: 12px;
  }
  .subcategory-description {
    display: none;
  }
  .product-grid-container {
    grid-template-columns: 1fr !important;
    padding: 10px;
  }

  /* Adjust product card image size on mobile */
  /* .MuiCardMedia-root {
    height: 200px !important;
  } */
}

@media (max-width: 480px) {
  /* For very small screens */
  /* .hero-job-container {
    padding: 15px;
  }

  .hero-text h1 {
    font-size: 18px;
    text-align: center;
  }

  .hero-text p {
    font-size: 12px;
  } */

  .Description {
    padding: 10px;
  }

  .description-title h2 {
    font-size: 20px;
  }

  .description-title p {
    font-size: 12px;
  }

  .description-content-1 {
    padding: 8px 10px; /* Compact padding for very small screens */
  }

  .description-content-1 h2 {
    text-align: left;
    font-size: 16px; /* Smaller heading */
  }

  .description-content-1 p {
    font-size: 12px; /* Smaller font size */
  }

  .description-content-1 ul {
    padding-left: 10px; /* Minimal padding for lists */
  }

  .description-content-1 li {
    font-size: 12px;
  }
  .grid-container {
    padding: 120px 20px 120px 20px;
    gap: 110px;
  }
  .page-container {
    flex-direction: column;
  }
  .collapsible-container {
    width: 100%;
    margin-left: -100px;
  }
  .right-side-content {
    width: 100%;
    margin-left: 10px;
  }
  .reviews-section {
    width: 100%;
    padding-left: 1rem;
  }
  .Shopping-cart-title h2 {
    font-size: 20px;
    margin-left: 10px;
  }

  .item-image {
    width: 60px; /* Smaller image size */
    height: 60px;
  }

  .item-title {
    font-size: 14px; /* Smaller title font size */
  }

  .item-info {
    font-size: 10px; /* Smaller info font size */
  }

  .unit-price,
  .quantity {
    font-size: 14px; /* Smaller price font size */
  }

  .quantity button {
    width: 30px; /* Adjust button size */
    height: 30px;
  }

  .price {
    font-size: 14px; /* Smaller price font size */
  }

  .delete-icon {
    font-size: 16px; /* Smaller delete icon */
    margin-top: 60px; /* Adjust margin */
  }
  .product-grid-container {
    grid-template-columns: repeat(1, 1fr);
    gap: 16px;
    padding-left: 50px;
  }
}
/* Responsive Design */
@media (max-width: 768px) {
  .job-form-card {
    padding: 2rem 3rem;
    max-width: 90%;
  }

  .job-form-title {
    font-size: 20px;
  }

  .job-form-field input,
  .job-form-field textarea {
    width: 90%;
  }

  .job-upload-button,
  .job-submit-button {
    width: 100%;
  }

  .job-form-row {
    flex-direction: column;
    gap: 0rem;
  }

  .image-preview-container {
    grid-template-columns: repeat(2, 1fr); /* 2 images per row */
  }
}

@media (max-width: 480px) {
  .job-form-card {
    padding: 1.5rem;
  }

  .job-form-title {
    font-size: 18px;
  }

  .job-form-field label {
    font-size: 12px;
  }

  .job-form-field input,
  .job-form-field textarea {
    font-size: 12px;
  }

  .job-upload-button,
  .job-submit-button {
    font-size: 12px;
    padding: 0.5rem;
  }
  .job-form-row {
    flex-direction: column;
    gap: 0rem;
  }
  .image-preview-container {
    grid-template-columns: repeat(1, 1fr); /* 1 image per row */
  }
}
/* Responsive Design */
@media (max-width: 768px) {
  .faq-container {
    padding: 0 1rem;
    max-width: 100%;
  }

  .faq-tabs {
    flex-direction: row; /* Stack tabs vertically */
  }

  .faq-tab {
    font-size: 14px; /* Slightly larger text for better readability */
    padding: 10px;
  }

  .faq-item {
    padding: 10px 15px;
    max-width: 100%;
  }

  .faq-question {
    font-size: 16px;
  }

  .faq-answer {
    font-size: 14px;
  }
  .concept-cards-container {
    font-family: Horizon;
    gap: 0; /* Remove spacing */
  }

  .concept-card {
    display: none; /* Hide all cards by default */
  }

  .middle-card {
    display: block; /* Show only the middle card */
    width: 300px;
    height: 300px;
    transform: scale(1);
  }
  .concept-subtitle h4,
  h5 {
    font-size: 14px;
    text-align: left;
    margin-left: -20px;
  }
  .concept-subtitle-text {
    margin: 0;
    margin: auto;
    margin-left: -22px;
  }
  .concept-progress-container {
    padding-left: 120px;
  }
  .left-card,
  .right-card {
    display: none !important; /* Ensure they are hidden */
  }

  .concept-cards-container {
    justify-content: center; /* Ensure the middle card stays centered */
    gap: 0; /* Remove unnecessary gap */
  }
}

@media (max-width: 480px) {
  .faq-tabs {
    flex-direction: row; /* Stack tabs vertically for very small screens */
  }

  .faq-tab {
    font-size: 10px;
    padding: 8px;
  }

  .faq-question {
    font-size: 14px;
  }

  .faq-answer {
    font-size: 12px;
  }
  /* .slider-container-home {
    width: 80%;
    margin-left: 77px;
  } */
  .slider-controls-home {
    margin: auto;
  }
  .Caption-careers {
    padding: 50px 39px 10px 39px;
    font-size: 16px;
    line-height: 30px;
  }
  .progress-container-track {
    width: 100%;
  }
  .order-details {
    width: 100%;
    margin-left: 0;
  }
  .order-card-title {
    width: 100%;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 27px;
  }
  .suborder-item-details {
    flex-direction: row;
    gap: 1px;
    align-items: baseline;
  }
  .slider-container-home {
    width: 100%;
    padding: 0.2rem;
    overflow: hidden;
    margin: auto;
  }
  .slider-content {
    max-width: 55%;
  }

  .product-card {
    width: 100%;
  }

  .product-badge,
  .product-metadata {
    font-size: 0.4rem;
  }
  .slider-controls-home {
    flex-direction: column;
  }
  .product-image-home {
    width: 89%;
    margin: auto;
  }
  .checkout-form {
    width: 100%;
  }
  /*cgheckout billing */
  .Billinginfo_checkbox {
    width: 100%;
    margin-left: 0;
  }
  .billinginfo-form-container {
    width: 100%;
    margin-left: 0;
    flex-direction: column;
  }
  .form-navigation button {
    margin: 10px auto;
    border-radius: 10px;
  }
  .Ordersummary-firstrow-secondcolumn-secondrow {
    width: 100%;
    margin-left: 0;
  }
  .partners-logo-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .slider-title h1 {
    margin-left: 20px;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  /* Small laptop screens */
  .partner-Caption {
    padding: 60px;
  }

  .partner-Caption p {
    font-size: 16px;
  }

  .partners-second-section {
    padding: 0 40px 40px 40px;
  }

  .partners-second-section h2 {
    font-size: 28px;
  }

  .partners-second-section ul li {
    font-size: 18px;
  }
}
@media (min-width: 769px) and (max-width: 1024px) {
  .product-grid-container {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px;
  }
}
@media (max-width: 768px) {
  /* Tablet screens */
  .partner-Caption {
    padding: 40px;
  }

  .partner-Caption p {
    font-size: 15px;
  }

  .partners-second-section {
    padding: 0 20px 30px 20px;
  }

  .partners-second-section h2 {
    font-size: 24px;
  }

  .partners-second-section ul li {
    font-size: 16px;
  }
  .partners-logo-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  /* Mobile screens */
  .partner-Caption {
    padding: 20px;
  }

  .partner-Caption p {
    font-size: 14px;
  }

  .partners-second-section {
    padding: 0 15px 20px 15px;
  }

  .partners-second-section h2 {
    font-size: 20px;
  }

  .partners-second-section ul li {
    font-size: 14px;
  }
  .partners-section {
    flex-direction: column;
    padding: 20px;
  }
  .carousel-button.left {
    left: 100px;
  }

  .carousel-button.right {
    right: -100px;
  }
  .carousel-wrapper {
    width: 28%;
  }
  .related-img {
    width: 100%;
  }
  .carsousel-item {
    width: 207px;
  }
}
/* For tablets and small laptops */
/* For small laptops and tablets */
@media screen and (max-width: 1024px) {
  .terms-container {
    flex-direction: row; /* Stack sidebar and content */
    align-items: flex-start; /* Align items to the top */
    height: 100vh; /* Full viewport height */
  }

  .sidebar {
    width: 80%; /* Sidebar takes 80% of the container width */
    padding: 10px;
    text-align: center; /* Center align items */
    height: 100%; /* Sidebar height takes full screen */
  }

  .sidebar-item {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .content {
    width: 80%; /* Content takes 80% of the container width */
    padding: 20px;
    height: 100%; /* Content height should match sidebar height */
    overflow-y: auto; /* Make the content scrollable if it's too long */
    box-sizing: border-box; /* To include padding in the width/height calculation */
  }

  /* Ensure the sidebar and content align within the full height of the screen */
  .terms-container {
    display: flex;
    justify-content: flex-start;
    height: 100vh; /* Full height of the screen */
  }

  .sidebar {
    display: flex;
    flex-direction: column;
    width: 25%;
    padding: 10px;
    text-align: left;
  }

  .content {
    display: flex;
    flex-direction: column;
    padding: 20px;
    width: 75%;
    height: 100%; /* Makes the content take the full height */
    overflow-y: auto; /* Allows scrolling if content exceeds height */
  }
  .checkout-form {
    width: 100%;
  }
  /*cgheckout billing */
  .Billinginfo_checkbox {
    width: 100%;
    margin-left: 0;
  }
  .billinginfo-form-container {
    width: 100%;
    margin-left: 0;
    flex-direction: column;
  }
  .form-navigation button {
    margin: 10px auto;
    border-radius: 10px;
  }
}

/* For mobile devices */
@media (max-width: 768px) {
  .terms-container {
    flex-direction: column;
    height: auto;
    margin: 20px 10px; /* Reduce margins */
  }

  .sidebar {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    width: 100%;
    padding: 10px;
  }

  .sidebar-item {
    font-size: 12px; /* Smaller font size */
    margin-bottom: 5px;
    padding: 5px 10px;
  }

  .content {
    width: 100%; /* Full width */
    padding: 20px;
  }
  /*shiping*/
  .shipping-container {
    width: 100%;
  }
  .action-buttons-shipping {
    flex-direction: column;
    align-items: normal;
  }
  .carousel-container {
    margin-left: 0;
    padding: 20px;
  }
  .divider {
    display: none; /* Hide the divider on small screens */
  }
  .partners-section {
    flex-direction: column;
    padding: 20px;
  }
  .checkout-form {
    width: 100%;
  }
  /*cgheckout billing */
  .Billinginfo_checkbox {
    width: 100%;
    margin-left: 0;
  }
  .billinginfo-form-container {
    width: 100%;
    margin-left: 0;
    flex-direction: column;
  }
  .form-navigation button {
    margin: 10px auto;
    border-radius: 10px;
  }
  .shipping-form-container {
    width: 100%;
    padding: 10px 0px;
  }
  .checkout-form form,
  .shippingform-form-container {
    flex-direction: column;
    margin: 47px 0px;
  }

  .shippingform-form-row {
    flex-direction: column;
    gap: 0.5rem;
    width: 200%;
  }
  .Ordersummary-firstrow-secondcolumn-secondrow {
    width: 90%;
    margin-left: 0;
    margin: auto;
  }
  .Ordersummary-firstrow-firstcolumn {
    width: 90%;
    margin: 10px auto;
  }
  .Ordersummary-thirdrow {
    width: 90%;
    margin: auto;
  }
  .Ordersummary-firstrow {
    flex-direction: column;
  }
  .paymentmethod-container {
    flex-direction: column;
  }
  .paymentmethod-firstrow-firstcolumn {
    margin-bottom: 20px;
  }
  .carsousel-item {
    width: 207px;
  }
  .ordersummary-cart-item {
    flex-direction: column;
    align-items: normal;
  }
  .product-header,
  .product-row {
    gap: 20px;
  }
}
/* Hide navigation buttons on screens smaller than 1024px */
@media (max-width: 1023px) {
  .related-swiper .swiper-button-next,
  .related-swiper .swiper-button-prev {
    display: none !important;
  }
}
/* Mobile-specific styles for related products */
@media (max-width: 767px) {
  .related-products-container {
    padding: 24px 8px;
  }

  .related-swiper {
    width: 100%;
    overflow: visible;
  }

  .related-img {
    height: 200px;
  }

  .related-name {
    font-size: 16px;
  }

  .related-category {
    font-size: 12px;
  }

  .related-price {
    font-size: 14px;
  }

  .swiper-slide {
    width: 100% !important;
  }
  .slider-container-home {
    padding: 1.5rem 1rem;
  }

  .slider-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .bestseller-swiper {
    touch-action: pan-y;
  }

  .product-image-home {
    height: 10rem;
  }

  /* Pagination dots for mobile */
  .bestseller-swiper .swiper-pagination {
    position: relative;
    margin-top: 2rem;
  }

  .bestseller-swiper .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background: #ccc;
    opacity: 0.5;
  }

  .bestseller-swiper .swiper-pagination-bullet-active {
    opacity: 1;
    background: #6c7c59;
  }
  .product-info-bestseller {
    padding: 0.5rem 1rem;
  }
}

/* Vendor Profile Products Grid Responsive Styles */
@media (max-width: 768px) {
  .vendorProducts-grid {
    padding: 10px;
  }

  .Productsgrid-header {
    margin-bottom: 15px;
  }
}

@media (max-width: 480px) {
  .vendorProducts-grid {
    padding: 5px;
  }

  .Productsgrid-header {
    width: 100%;
  }
}
