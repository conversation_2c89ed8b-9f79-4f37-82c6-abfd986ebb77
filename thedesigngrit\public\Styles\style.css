@font-face {
  font-family: "Horizon";
  src: url("../fonts/horizon/horizon.woff2") format("woff2"),
    url("../fonts/horizon/horizon.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
  unicode-range: U+000-5FF; /* Latin character subset */
}
/* Login.css */
body,
html {
  margin: 0;
  font-family: "Horizon", Montserrat;
  overflow-y: scroll; /* Ensures the page scrolls vertically */
  scrollbar-width: none; /* Firefox */
}

body::-webkit-scrollbar {
  width: 0px;
  background: transparent;
  display: none; /* Chrome, Safari, and Edge */
}

body::-webkit-scrollbar:hover {
  width: 10px;
  background: rgb(216, 216, 216);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  display: flex;
  height: 100vh;
}
.container-login {
  display: flex; /* Enables flexbox */
  flex-direction: column;
  justify-content: center; /* Centers content horizontally */
  align-items: center; /* Centers content vertically */
  text-align: center;
  height: 100vh;
  background-image: url("/assets/signin.jpeg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover; /* Ensures the background image covers the entire container */
  position: relative;
  overflow: hidden;
}

.container-login::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(5px); /* Adds blur to the background */
  background-color: rgba(
    0,
    0,
    0,
    0.4
  ); /* Adds a semi-transparent dark overlay */
  z-index: 1;
}
/* Login error message styling */
.login-error-message {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  border-left: 4px solid #f44336;
  animation: fadeIn 0.3s ease;
  text-align: left;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhance existing error message styling */
.error-message-login {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
  margin-bottom: 12px;
  text-align: left;
  animation: fadeIn 0.3s ease;
}
.login-form {
  position: relative;
  z-index: 2; /* Keeps the form above the background and blur overlay */
  background-color: rgba(108, 124, 89, 0.8); /* Semi-transparent sage green */
  padding: 60px 20px 0px 20px;
  width: 50%; /* Adjust the width of the form */
  height: 60%;
  max-width: 400px; /* Limits the width on larger screens */
  border-radius: 10px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2); /* Adds shadow for depth */
  text-align: center;
  display: flex;
  color: #2d2d2d;
  flex-direction: column; /* Stacks child elements vertically */
  justify-content: center; /* Ensures elements are vertically aligned */
  align-items: center; /* Centers child elements horizontally */
}
.signup-form-container {
  position: relative;
  z-index: 2; /* Keeps the form above the background and blur overlay */
  background-color: rgba(108, 124, 89, 0.8); /* Semi-transparent sage green */
  padding: 50px 0px 40px 0px;
  width: 30%; /* Adjust the width of the form */
  height: 80%;
  border-radius: 10px;
  text-align: center;
  display: flex;
  color: #2d2d2d;
  flex-direction: column; /* Stacks child elements vertically */
  justify-content: center; /* Ensures elements are vertically aligned */
  align-items: center; /* Centers child elements horizontally */
}
.eye-icon {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  cursor: pointer;
  color: #666;
  z-index: 1;
}
.signinLogo {
  position: relative;
  z-index: 2;
}
.signinLogo img {
  width: 180px;
  height: 80px;
}
.form-title-signup {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  font-family: "Horizon";
  margin-top: 100px;
  margin-bottom: 0px;
}
.form-title-signin {
  font-size: 2rem;
  font-weight: bold;
  font-family: "Horizon";
  text-align: center;
  padding-top: 70px;
  font-family: "Horizon";
}
.logo img {
  width: 100%;
  height: 100%;
  padding-bottom: 50px;
  text-align: center;
}

.signin-form {
  width: 300px;
  text-align: center;
  padding-bottom: 130px;
}

.social-btn {
  width: 100%;
  padding: 10px;
  margin: 10px 0;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}
.social-btns-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.google-btn {
  background-color: #ffffff;
  color: #2d2d2d;
  border-radius: 8px;
  text-align: center;
  font-family: Montserrat;
  font-weight: 400;
  display: flex;
  flex-direction: row;
  width: 94%;
  margin: auto;
  align-items: center;
}
.google-btn:hover,
.facebook-btn:hover {
  background-color: #e0e0e0;
}
.google-icon {
  padding-right: 0px;
  font-size: 30px;
}
.facebook-btn {
  width: 94%;
  margin: auto;
  background-color: #ffffff;
  color: #2d2d2d;
  border-radius: 8px;
  text-align: center;
  font-family: Montserrat;
  font-weight: 400;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.facebook-icon {
  color: blue;
  font-size: 30px;
  padding-right: 10px;
}

.divider-signIn {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 10px 0;
  color: #eae3e4; /* Text color for "OR" */
  font-weight: bold;
  font-size: 1rem;
  font-family: "Montserrat"; /* Ensure the font styling is applied */
  position: relative;
  text-transform: uppercase; /* Makes the text all caps for style consistency */
}

.divider-signIn::before,
.divider-signIn::after {
  width: 100%;
  content: "";
  flex: 1; /* Makes the lines evenly stretch */
  height: 1px; /* Line thickness */
  background-color: #eae3e4; /* Line color */
  margin-left: 5px;
  margin-right: 5px;
}
.input-field {
  width: 93%;
  padding: 10px;
  margin: 10px 0;
  border: 1px solid #f2e9e4;
  border-radius: 8px;
  font-family: Montserrat;
  font-weight: 400;
}
.input-field :focus {
  border-color: #f2e9e4;
}
.signin-btn {
  width: 94%;
  padding: 10px;
  margin: 5px 0px 10px 0px;
  background-color: #2d2d2d;
  color: #eae3e4;
  border: none;
  cursor: pointer;
  border-radius: 8px;
}
.signin-btn:hover {
  transform: translateY(-3px);
  background-color: #eae3e4;
  color: #2d2d2d;
  border: 2px solid #2d2d2d;
}
.register-policy-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.register-link {
  margin-top: 10px;
  font-size: 0.9rem;
  font-family: Montserrat;
  font-weight: 400;
}

.register-link a {
  color: #efebe8;
  text-decoration: none;
}
/*Signup form*/
.signup-logo {
  font-weight: bold;
  text-align: center;
  margin: auto;
  padding-top: 20px;
  padding-bottom: 20px;
}
.signup-logo img {
  width: 50%;
  height: 50%;
  text-align: center;
}

.signup-form {
  width: 300px;
  text-align: center;
}
.register-policy-section {
  display: flex;
  align-items: center;
  justify-content: center;
}
.register-policy {
  font-size: 10px;
  font-family: Montserrat;
  text-align: justify;
  color: #2d2d2d;
}
.register-policy span {
  font-size: 10px;
  font-family: Montserrat;
  text-align: center;
  color: #2d2d2d;
}
.register-policy a {
  font-size: 10px;
  color: #efebe8;
  text-decoration: none;
  font-weight: bold;
}

.register-link {
  margin-top: 20px;
  font-size: 0.9rem;
  font-family: Montserrat;
  font-weight: 400;
}

.register-link a {
  color: #efebe8;
  text-decoration: none;
}

.product-showcase {
  color: white;
}

.product-showcase h2 {
  margin-bottom: 1rem;
  font-family: Horizon;
  font-weight: bold;
  text-align: center;
  font-size: 30px;
}

.features {
  list-style: none;
  padding: 0;
  margin: 0 0 1rem;
  font-family: Horizon;
  font-weight: bold;
  color: #2d2d2d;
  font-size: 10px;
  padding-left: 5px;
}

.features li {
  margin-bottom: 0.5rem;
  margin-left: 2px;
}
.checkIcons {
  color: white;
  padding-right: 5px;
}

/* Nav Bar*/

.header-container {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0); /* Ensure background is solid */
  transition: background-color 0.3s ease, box-shadow 0.3s ease,
    backdrop-filter 0.3s ease;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  font-family: Montserrat;
  z-index: 2;
  transition: background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    backdrop-filter 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-container.sticky {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000; /* or higher to stay above everything */
  background: white; /* Or whatever background you want */

  transition: background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    backdrop-filter 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 3rem;
  padding: 30px;
  box-sizing: border-box;
  z-index: 2;
  overflow: hidden;
  transition: background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* For desktop (multiple cards in a row) */
.desktop-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

/* For mobile (single card in a row) */
.mobile-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.logo img {
  position: relative;
  width: 134px;
  height: 89px;
  padding: 35px 30px 10px 20px;
  align-items: center;
  z-index: 2;
}

.logo span {
  font-weight: 300;
}

.search-bar {
  display: flex;
  align-items: center;
  border: 2px solid #2d2d2d;
  border-radius: 6px;
  padding: 5px 15px;
  width: 55%;
  font-family: Montserrat;
}
/* Smooth Scroll Behavior */
html {
  scroll-behavior: smooth;
}
/* Dropdown Container */
.suggestions-dropdown {
  position: absolute;
  top: 50%; /* Places it right below the search bar */
  width: 50%;
  background-color: #fff;
  background-color: rgba(
    255,
    255,
    255,
    0.93
  ); /* Slightly transparent background */
  backdrop-filter: blur(10px); /* Apply blur effect to the background */
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  max-height: 250px; /* Limits height to prevent overflow */
  overflow-y: auto; /* Scrollable if many results */
  border-radius: 20px;
  z-index: 10000; /* Keeps it above other elements */
}

/* Each Suggestion Item */
.suggestion-item {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  transition: background 0.2s;
  border-bottom: 1px solid #ccc;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background: #f5f5f5;
}

/* Product Image */
.suggestion-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 10px;
}

/* Text Container */
.suggestion-text {
  display: flex;
  flex-direction: column;
}

/* Product Name */
.suggestion-name {
  font-weight: bold;
  font-size: 16px;
}

/* Product Category */
.suggestion-category {
  color: gray;
  font-size: 14px;
}
.suggestion-info {
  color: gray;
  font-size: 12px;
}
.icon-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 6px;
}
/* Favorites overlay styling */
.notification-actions-vendor {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.remove-favorite-button {
  background-color: transparent;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-favorite-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

/* Animation for the badge */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Make the favorites item more interactive */
.notification-item-vendor {
  transition: background-color 0.2s ease;
  position: relative;
}

.notification-item-vendor:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Update the header to show count */
.overlay-header-vendor h3 {
  display: flex;
  align-items: center;
  gap: 8px;
}
.avatar {
  background-color: #e0e0e0;
}

.Signup-btn-navbar {
  background-color: #2d2d2d !important;
  color: #eae3e4 !important;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  font-family: Montserrat;
  font-weight: 400;
  font-size: 15px;
  padding: 10px 20px;
}

.header-bottom {
  display: flex;
  justify-content: center;
  gap: 35px;
  align-items: center;
  width: 100%;
  font-family: Montserrat;
  padding: 10px;
  z-index: 0;
}

.category {
  font-size: 15px !important;
  cursor: pointer;
  transition: all 0.3s ease; /* Smooth transition for all changes */
  justify-content: flex-start;
  text-align: left;
  font-family: "Montserrat";
  font-weight: 400;
}

.category.highlighted {
  background-color: #ffffff;
  color: #000000;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  box-shadow: 0 0px 8px rgba(0, 0, 0, 0.2); /* Add box-shadow */
  padding: 6px 10px 3px 10px; /* Same padding as the default to prevent shifting */
}

.hamburger {
  display: none; /* Hide by default */
}

/* Dropdown Menu */
.mobile-menu {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 60px;
  right: 0;
  width: 100%;
  z-index: 10;
}

.menu-item {
  padding: 10px 20px;
  text-align: left;
  font-size: 16px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.hamburger {
  display: none;
}
/* .category:hover .category.highlighted:hover {
  background-color: #ffffff;
  color: #000000;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); 
  padding: 7px 7px;
} */

/*Home Page*/
.hero-video {
  position: relative;
  width: 100%;
  height: 70vh;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border-radius: 20px;
  margin-bottom: 50px;
  margin-top: 30px;
}

.hero-video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-text-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  text-align: center;
  z-index: 2;
}

.btn-hero-section {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.home {
  display: block;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: auto;
  width: 100%;
}

.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("/Assets/Video-hero/herovideo.mp4");
  background-size: cover;
  background-position: center;
  filter: blur(20px); /* Apply the blur here */
  z-index: 1; /* Keep it behind other content */
}

.hero-home-section {
  position: relative;
  width: 70%;
  height: auto; /* Adjust height based on your design */
  overflow: hidden;
  border-radius: 20px;
  margin-top: 1rem;
  z-index: 2;
  margin: auto;
}

.hero-slider {
  width: 100%;
}

.hero-slide {
  width: 100%;
  flex-shrink: 0;
  position: relative;
}

.hero-image {
  width: 100%;
  height: 600px;
  object-fit: cover;
  filter: brightness(80%);
}

.hero-text-overlay {
  position: absolute;
  top: 50%;
  left: 30%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  text-align: left;
}

.hero-text-overlay h1 {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #ffffff;
  text-align: left;
}

.hero-text-overlay p {
  font-size: 1.2rem;
  max-width: 600px;
  line-height: 1.5;
  font-family: Montserrat;
}

.btn-hero-section {
  border: 1px solid #6c7c59;
  background-color: #6c7c59;
  padding: 10px;
  border-radius: 10px;
  margin-top: 20px;
  cursor: pointer;
}

.slider-navigation {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.slider-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #fff;
  opacity: 0.5;
  cursor: pointer;
  transition: opacity 0.3s;
}

.slider-dot.active {
  opacity: 1;
  background-color: #6c7c59;
}
.slick-dots li button:before {
  color: #6c7c59; /* Change dots color */
}

.slick-dots li.slick-active button:before {
  opacity: 1;
  color: #6c7c59; /* Active dot color */
}
.concept-section {
  height: 100%;
  width: 100%;
  padding: 0px;
}
.concept-title h2 {
  text-align: right;
}

/*Category*/
.shop-by-category {
  padding: 20px;
  max-width: 1400px; /* Center content on larger screens */
  width: 100%; /* Full width for smaller screens */
  display: flex;
  flex-direction: column;
  text-align: center;
  margin: 7rem auto;
  box-sizing: border-box;
}

.shop-by-category h2 {
  font-family: Horizon;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: left;
  padding: 10px;
  color: #2d2d2d;
}

/* Grid Layout */
.category-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 3 items per row */
  gap: 30px; /* Even spacing between items */
  justify-content: center; /* Center the grid on the screen */
  margin: 12px; /* Center horizontally */
}

/* Individual Card Styling */
.category-card {
  position: relative;
  background-size: cover;
  background-position: center;
  border-radius: 16px;
  height: 400px; /* Adjust card height for better visuals */
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.category-card:hover {
  transform: scale(1.05);
}

/* Overlay Content */
.card-content {
  color: white;
  width: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Title Styling */
.card-content h3 {
  font-size: 1.5rem;
  font-weight: bold;
  font-family: Horizon;
  margin: 0;
  text-align: left;
  text-transform: uppercase;
}

/* Button Styling */
.shop-button {
  background: #6b7b58; /* Green button color */
  color: white;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-family: Montserrat;
  font-weight: 600;
  text-align: center;
  transition: background 0.3s ease;
  width: 30%;
}

.shop-button:hover {
  background: #5a6a47; /* Slightly darker green on hover */
}

/* Responsive Adjustments */
@media screen and (max-width: 1440px) {
  .category-grid {
    grid-template-columns: repeat(
      3,
      1fr
    ); /* Keep 3 columns for screens smaller than 16 inches */
    gap: 25px; /* Slightly reduce gap */
  }

  .category-card {
    height: 350px; /* Adjust card height */
  }
}

@media screen and (max-width: 1024px) {
  .category-grid {
    grid-template-columns: repeat(2, 1fr); /* 2 columns on tablets */
    gap: 20px;
  }

  .category-card {
    height: 300px;
  }
}

@media screen and (max-width: 768px) {
  .category-grid {
    grid-template-columns: 1fr; /* Single column on mobile screens */
    gap: 15px;
  }

  .category-card {
    height: 250px;
  }

  .card-content h3 {
    font-size: 1.2rem;
  }

  .shop-button {
    width: 25%;
    font-size: 0.8rem;
  }
}

/*Conecpts Section*/
.concept-explore-container {
  padding: 20px clamp(10px, 5vw, 80px); /* Adjust padding dynamically */
  max-width: 100%; /* Prevents overflow */
  box-sizing: border-box; /* Ensures padding doesn't add extra width */
  background-color: #fff;
  position: relative;
  margin-top: 15rem;
}

.concept-title-container {
  display: flex;
  justify-content: end;
  align-items: center;
  gap: 5vw; /* Adjusts spacing dynamically */
  flex-wrap: wrap; /* Ensures content doesn't overflow */
  margin: 40px 0;
  padding-bottom: 20px;
  padding-top: 80px;
}

.concept-title {
  font-family: Horizon !important;
  font-size: 28px !important;
  font-weight: Bold !important;
  text-transform: uppercase;
}

.concept-cards-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 120px;
  margin: 40px 0;
  padding-bottom: 20px;
}

.concept-card {
  position: relative;
  border-radius: 20px !important;
  transition: all 0.4s ease;
  transform-origin: center;
  opacity: 0.8;
  overflow: hidden; /* Ensure the overlay doesn't spill out of the card */
  background-color: transparent;
}

.concept-card img {
  width: 100%;
  height: 100%;
  background-color: transparent;
}

/* Node animation for concepts */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(107, 123, 88, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(107, 123, 88, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(107, 123, 88, 0);
  }
}

.concept-card-content {
  position: absolute; /* Position within the card */
  bottom: 10px; /* Align near the bottom */
  left: 10px; /* Align near the left */
  display: flex; /* Horizontal layout for icon and text */
  align-items: center; /* Vertically align content */
  gap: 10px; /* Add spacing between icon and text */
  padding: 10px; /* Add padding for the content area */
  border-radius: 8px; /* Rounded corners for overlay */
  z-index: 2; /* Ensure it stays above the overlay */
}

.middle-card {
  width: 400px;
  height: 400px;
  opacity: 1;
  transform: scale(1.1);
  z-index: 3;
}

.left-card {
  width: 335px;
  height: 335px;
  transform: translateY(-20%) scale(0.9);
  z-index: 2;
}

.right-card {
  width: 335px;
  height: 335px;
  transform: translateY(37%) scale(0.9);
  z-index: 2;
}

.concept-card-content {
  position: absolute; /* Allow positioning within the parent */
  left: 10px; /* Align content near the left of the card */
  display: flex; /* Enable horizontal layout */
  align-items: center; /* Vertically align icon and text */
  gap: 10px; /* Add spacing between the icon and text */
  padding: 10px; /* Add padding around the content */
  border-radius: 8px; /* Rounded corners for overlay */
  z-index: 2; /* Ensure it stays above the image */
}

.concept-shopping-icon {
  color: #2d2d2d !important; /* Icon color */
  font-size: 15px; /* Adjust icon size */
  border: 1px white !important;
  background-color: #e5e4e2 !important;
}

.concept-card-text {
  color: #eae3e4; /* Text color */
  font-size: 15px; /* Adjust text size */
  font-weight: bold !important; /* Slightly bold text */
  font-family: Montserrat;
}

.concept-shopping-icon:hover {
  background: rgba(0, 0, 0, 0.7);
}

.concept-subtitle h4 {
  font-size: 22px;
  text-align: left;
}

.concept-subtitle-title {
  font-size: 30px;
  font-family: "Horizon" !important;
  font-weight: bold !important;
  line-height: 1.4;
}

.concept-subtitle-text {
  font-family: "Montserrat" !important;
  font-size: 18px;
  color: #666;
  font-weight: lighter;
  line-height: 1.5;
}

.concept-controls {
  display: flex;
  justify-content: space-between; /* Space out the progress bar and arrows */
  align-items: center; /* Vertically align all elements */
  margin-top: -80px;
}

.concept-progress-container {
  display: flex;
  justify-content: center; /* Center the progress bar and labels */
  align-items: center; /* Align items vertically */
  gap: 10px;
  flex-grow: 1; /* Let the progress bar area expand to fill space */
  padding-left: 90px;
}

.concept-progress-bar {
  width: 30%; /* Adjust the width as necessary */
  height: 8px;
  background: #e0e0e0;
  overflow: hidden;
  padding-left: 20px;
  transition: width 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

.concept-progress-fill {
  height: 100%;
  background: #6b7b58;
  position: absolute;
  top: 0;
  left: 0;
  width: 20%; /* Start empty */
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.concept-navigation {
  display: flex;
  justify-content: flex-end; /* Align navigation to the end */
  gap: 20px;
}

/*HEro sections */
.hero-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 50px;
  height: 329px;
  background-color: #6b7b58; /* Change background color as needed */
}

.hero-text {
  flex: 1;
  max-width: 50%; /* Adjust as needed */
  padding-left: 100px;
  color: #abc48c;
}

.hero-title {
  font-size: 3.5rem; /* Adjust as needed */
  margin: 0;
  color: white;
  text-align: left;
}

.hero-subtitle {
  font-size: 18px; /* Adjust as needed */
  margin: 10px 0 0;
  font-family: Montserrat;
}

.hero-section-image {
  flex: 1;
  max-width: 50%;
  object-fit: cover; /* Adjust as needed */
  padding-right: 40px;
}

.hero-section-image img {
  height: 260px; /* Maintain aspect ratio */
  border-radius: 20px;
  margin-left: 350px;
  margin-right: 70px;
  width: 300px;
}
.Caption-AboutUs {
  text-align: justify;
  padding: 0px 140px 0px 140px;
  font-size: 25px;
  line-height: 40px;
  font-family: "Montserrat";
}
.Caption-careers {
  text-align: justify;
  padding: 100px 140px 100px 140px;
  font-size: 25px;
  font-family: "Montserrat";
}
.ourMission-Section {
  display: flex; /* Use flexbox to align items in a row */
  flex-direction: row; /* Arrange items in a row */
  padding: 120px; /* Add padding to the section */
  align-items: center; /* Center items vertically */
}

.ourMission-Section-image {
  flex: 1; /* Allow the image section to take up space */
}

.ourMission-Section-image img {
  width: 100%; /* Make the image responsive */
  height: auto; /* Maintain aspect ratio */
}

.ourMission-Section-typo {
  flex: 1; /* Allow the text section to take up space */
  padding-left: 200px; /* Add some space between image and text */
  justify-content: space-between;
  gap: 20px;
}

.ourMission-Section-typo h2 {
  font-size: 2rem; /* Adjust title size */
  margin-bottom: 30px; /* Space below the title */
  color: #6c7c59;
  font-family: Horizon;
  font-weight: bold;
  text-align: start;
  margin-top: 30px;
}

.ourMission-Section-typo p {
  font-size: 20px; /* Adjust paragraph size */
  line-height: 1.5; /* Improve readability */
  font-family: montserrat;
}
.ourStory-Section-typo {
  flex: 1; /* Allow the text section to take up space */
  padding-left: 120px; /* Add some space between image and text */
  padding-right: 100px;
  margin-bottom: 60px;
}

.ourStory-Section-typo h2 {
  font-size: 2rem; /* Adjust title size */
  margin-bottom: 30px; /* Space below the title */
  color: #6c7c59;
  font-family: Horizon;
  font-weight: bold;
  text-align: start;
  margin-top: 30px;
}

.ourStory-Section-typo p {
  font-size: 22px; /* Adjust paragraph size */
  line-height: 1.5; /* Improve readability */
  font-family: Montserrat;
}
.line-between {
  border: 1px solid #ccc;
  width: 100%;
  margin: 60px auto;
}
.OurTeam-section {
  padding: 50px;
}
.OurTeam-section h2 {
  text-align: center;
  font-size: 2rem;
  color: #6c7c59;
  font-family: Horizon;
  font-weight: bold;
  margin-top: 30px;
}
.OurTeam-section p {
  font-family: Montserrat;
  font-weight: 200;
  margin-top: 50px;
  text-align: justify;
  padding: 10px 100px 10px 100px;
  font-size: 25px;
}
.meet-our-team {
  text-align: center; /* Center align the section */
  padding: 20px 20px; /* Add padding */
}

.team-members {
  display: flex; /* Use flexbox to align team members */
  justify-content: center; /* Center align team members */
  gap: 20px; /* Space between team members */
}
.team-member p {
  font-family: Montserrat;
  font-weight: 200;
  margin-top: 50px;
  text-align: justify;
  padding: 0px 0px 0px 0px;
  font-size: 20px;
}
.team-member {
  border-radius: 10px; /* Rounded corners */
  padding: 20px; /* Padding inside each member box */
  width: 406px; /* Fixed width for each member */
  gap: 200px;
}

.team-member-image {
  width: 150px; /* Set a fixed width for the image */
  height: 162px; /* Set a fixed height for the image */
  border-radius: 50%; /* Make the image circular */
  overflow: hidden; /* Hide overflow */
  margin: 0 auto; /* Center the image */
}

.team-member-image img {
  width: 100%; /* Make image responsive */
  height: auto; /* Maintain aspect ratio */
}

.team-member-name {
  font-size: 1.5rem; /* Adjust name size */
  margin: 20px 0 5px; /* Spacing for name */
}
.team-member-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}
.team-member-title {
  font-size: 1.2rem; /* Adjust title size */
  margin: 5px 0; /* Spacing for title */
}

.team-member-subtitle {
  font-size: 1rem; /* Adjust subtitle size */
  margin: 10px 0 10px; /* Spacing for subtitle */
}
.team-member-icons {
  display: flex;
  flex-direction: row;
  gap: 10px;
}
.linkedin-icon {
  color: #0077b5; /* LinkedIn blue color */
  font-size: 1.5rem; /* Adjust icon size */
  text-decoration: none; /* Remove underline */
}

.linkedin-icon:hover {
  color: #005582; /* Darken on hover */
}
.instagram-icon {
  color: #2d2d2d; /* LinkedIn blue color */
  font-size: 1.5rem; /* Adjust icon size */
  text-decoration: none; /* Remove underline */
}
.instagram-icon:hover {
  color: #2d2d2d; /* Darken on hover */
}

/* Sustainability Section */
.sustainability-section {
  display: flex;
  flex-direction: row;
  width: 100vw; /* Ensures full viewport width */
  max-width: 100%; /* Prevents unintended margins */
  height: auto;
  background-color: #ebe4e5;
  margin: 0; /* Removes any margin */
  padding: 0; /* Removes any padding */
  overflow-x: hidden; /* Ensures no horizontal overflow */
}

/* Image Container */
.sustainability-image-container {
  width: 50%; /* Set width to 50% */
  height: 50%;
  position: relative;
}

.sustainability-image {
  width: 100%; /* Fill the container */
  height: 100%;
  object-fit: cover;
}

/* Content Container */
.sustainability-content-container {
  width: 50%; /* Set width to 50% */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.sustainability-content-wrapper {
  max-width: 36rem;
  margin: 0 auto;
}

/* Styling for Text Content */
.sustainability-label {
  display: block;
  color: #666;
  font-size: 1.125rem;
  font-family: Montserrat;
}

.sustainability-heading {
  margin: 1rem 0;
  font-size: 2rem;
  font-weight: 200;
  color: #2d2d2d;
  line-height: 1.2;
  font-family: Montserrat;
}

.sustainability-description {
  color: #4a4a4a;
  font-size: 1.125rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  font-family: Montserrat;
}

.sustainability-button {
  background-color: #6c7c59;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.sustainability-button:hover {
  background-color: #5b6b4e;
}

/* Media Query for Responsive Layout */
@media screen and (max-width: 768px) {
  .sustainability-section {
    flex-direction: column; /* Switch to column layout */
  }

  .sustainability-image-container {
    width: 100%; /* Take full width in column layout */
  }

  .sustainability-content-container {
    width: 100%; /* Take full width in column layout */
    padding: 1rem;
  }
}

/* Media Queries for responsive design */

/* For larger screens */
@media (min-width: 1024px) {
  .sustainability-content-container {
    padding: 6rem;
  }

  .sustainability-heading {
    font-size: 4rem;
  }
}
/* Partner Section HomePage*/
.partners-section {
  padding: 4rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 4rem;
}

.partners-content {
  flex: 0 1 45%;
  max-width: 600px;
}

.partners-heading {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  color: #1a1a1a;
  line-height: 1.2;
  letter-spacing: 0.5px;
}

.partners-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #4a4a4a;
  margin-bottom: 2rem;
  font-family: Montserrat;
}

.partners-button {
  background-color: #6b7f5e;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 1rem;
}

.partners-button:hover {
  background-color: #5a6b4f;
}

/* Logo grid section */
.partners-logo-grid {
  flex: 0 1 50%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 2rem;
  align-items: center;
}

.partner-logo-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  transition: transform 0.3s ease;
}

.partner-logo-link:hover {
  transform: scale(1.05);
}

.partner-logo {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

/* Optional: Add hover effect for logos */
.partner-logo {
  filter: grayscale(0);
  transition: filter 0.3s ease;
}

.partner-logo:hover {
  filter: grayscale(20%);
}

/*Best seller homePage*/
.slider-container-home {
  width: 100%;
  padding: 2rem;
  overflow: hidden;
  background-color: #fff;
  margin-bottom: 3rem;
  box-sizing: border-box;
  position: relative;
}

.slider-title {
  max-width: 100%;
  margin: 0 auto;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 2rem;
  font-family: Horizon;
  text-align: left;
  color: #2d2d2d;
}
.silder-title h2 {
  font-family: Horizon;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: left;
  padding: 10px;
  color: #2d2d2d;
}
.slider-wrapper {
  overflow: hidden;
  width: 100%;
  position: relative;
}

.slider-content {
  display: flex;
  gap: 1rem;
  transition: transform 0.3s ease-in-out;
  will-change: transform;
}
.bestseller-swiper {
  width: 100%;
  overflow: visible;
  touch-action: pan-y;
}
/* .product-card {
  flex: 0 0 calc((100% - 2rem) / 5); 
  box-sizing: border-box;
  transition: transform 0.3s ease-in-out;
  cursor: pointer;
} */
.product-grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  width: 100%;
}
.product-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
}
.product-card-bestseller {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}
.product-card-bestseller:hover {
  transform: translateY(-5px);
}
.product-image-home {
  width: 100%;
  height: 13rem;
  border-radius: 8px;
  overflow: hidden;
}
.product-image-home img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.product-badge {
  font-size: 0.5rem;
  color: #2d2d2d;
  background-color: #ffffff;
  border: 1px solid #2d2d2d;
  border-radius: 4px;
  padding: 4px 8px;
  display: inline-block;
  margin-bottom: 8px;
  text-transform: uppercase;
  font-family: Montserrat;
}

.product-info-bestseller {
  padding: 0.5rem 0;
}

.product-metadata {
  color: #666;
  font-size: 0.5rem;
  margin-bottom: 0.5rem;
  font-family: Montserrat;
}

.product-title-bestseller {
  font-size: 0.8rem;
  margin: 0.5rem 0;
  font-family: "Montserrat";
}

.product-price-bestseller {
  font-size: 0.6rem;
  color: #2d2d2d;
  font-weight: 500;
  font-family: Montserrat;
}

.slider-controls-home {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.dots {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ccc;
  border: none;
  padding: 0;
  cursor: pointer;
}

.dot.active {
  background-color: #2d2d2d;
}

.arrows-home {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.arrow-home {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #2d2d2d;
  cursor: pointer;
  padding: 0.5rem;
}

.arrow-home:hover {
  opacity: 0.7;
  background-color: transparent;
  color: #2d2d2d;
}

/* footer css */
.logo-Footer img {
  width: 400px;
  height: 200px;
  margin-left: 0px;
}
.subscribe-contatiner {
  margin-left: 50px;
  margin-top: 30px;
}

.Textcontainer {
  margin-left: 70px;
  margin-top: 100px;
}

.footer-container {
  background-color: #eae3e4;
  width: 100%;
}

/* Job Description Page */
.job-Page {
  display: flex;
  flex-direction: column;
}

.hero-job-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 50px;
  height: 200px;
  background-color: #6b7b58;
}

.Description {
  padding: 50px;
}

.description-title {
  font-size: large;
  text-align: center;
}

.description-title p {
  font-family: Montserrat;
}

.description-content-1 {
  font-family: Montserrat;
  padding: 20px 40px;
  margin: auto;
}

.description-content-1 h2 {
  padding-left: 120px;
  font-family: Horizon;
  text-align: left;
}

.description-content-1 p {
  font-size: 17px;
  padding-left: 120px;
  padding-right: 150px;
  line-height: 200%;
}

/*FAQs page*/
.hero-job-container-faq {
  display: flex;
  justify-content: center; /* Horizontally center */
  align-items: center; /* Vertically center */
  text-align: center;
  background-color: #6b7b58;
  padding: 50px 0px;
}

.hero-text-faq {
  max-width: 50%; /* Adjust width as needed */
  padding: 20px; /* Optional: add some padding for spacing */
  color: #abc48c;
}

.hero-title-faq {
  font-size: 2.5rem; /* Adjust as needed */
  margin: 0;
  text-align: center;
  color: white;
}

.hero-subtitle-faq {
  font-size: 15px; /* Adjust as needed */
  margin: 10px 0 0;
  text-align: center;
  font-family: Montserrat;
}
/* FAQ.css */

.faq-container {
  width: 800px; /* Fixed width for consistency */
  margin: 50px auto;
  padding: 0;
  font-family: Montserrat;
  box-sizing: border-box; /* Prevents padding or borders from affecting width */
}

.faq-tabs {
  display: flex;
  background-color: #ffffff;
  border-top: 1px solid #ddd;
  border-right: 1px solid #ddd;
  border-left: 1px solid #ddd;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  width: 40%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.faq-tab {
  flex: 1;
  padding: 15px 15px;
  width: 30%;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  color: #333;
  background-color: #ffffff;
  border: none;
  border-right: 0px solid #ddd;
  margin-top: 0px;
  margin-bottom: 0px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  margin-bottom: 0px;
}

.faq-tab:last-child {
  border-right: none;
}

.faq-tab.active {
  background-color: #6a8452;
  color: #fff;
}

.faq-tab:hover {
  background-color: #8fab74;
  color: #ffffff;
}

.faq-content {
  border: 1px solid #ddd;
  border-radius: 0 15px 15px 15px;
  background-color: #ffffff;
  padding: 10px 0;
  box-shadow: 0 8px 10px rgba(0, 0, 0, 0.3);
  transition: 0.3s ease;
}

.faq-item {
  padding: 15px 30px;
  width: 80%;
  margin: 0 auto;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
  transition: 0.3s ease;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-item:hover {
  background-color: none;
}

.faq-item.active {
  transition: max-height 0.3s ease;
  max-height: 1000px; /* This is the expanded height; adjust based on content */
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.faq-toggle-icon {
  font-size: 24px;
  font-weight: bold;
  color: #6a8452;
}

.faq-answer {
  margin-top: 10px;
  font-size: 16px;
  color: #555;
  line-height: 1.5;
  max-height: 0; /* Initially collapsed */
  overflow: hidden; /* Prevents content from being displayed when collapsed */
  transition: max-height 0.3s ease-out; /* Smooth transition for expanding/collapsing */
}

.faq-item.active .faq-answer {
  max-height: 1000px; /* Set a sufficiently large max-height when expanded */
}

/*Job Form*/
.job-form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
}

.job-form-card {
  background: white;
  padding: 3rem 5rem;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 24px 24px 24px rgba(0, 0, 0, 0.08);
}

.job-form-title {
  font-family: "Horizon";
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 2rem;
  letter-spacing: 0.5px;
}

.job-form-field {
  margin-bottom: 1.5rem;
}

.job-form-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}

.job-form-field input,
.job-form-field textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  color: #2d2d2d;
  background: white;
  font-family: "Montserrat";
}

.job-form-field input::placeholder,
.job-form-field textarea::placeholder {
  color: #999;
}

.job-form-field input:focus,
.job-form-field textarea:focus {
  outline: none;
  border-color: #6b7b58;
}

.job-form-row {
  display: flex;
  gap: 5rem;
  margin-bottom: 1.5rem;
}

.job-form-row .form-field {
  flex: 1;
  margin-bottom: 0;
}

.job-upload-button {
  display: flex; /* Use flexbox to align items */
  align-items: center; /* Vertically align icon and text */
  justify-content: center; /* Center both elements */
  gap: 8px; /* Space between the icon and text */
  width: 95%;
  color: white;
  background-color: #2d2d2d; /* Blue background for the button */
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  font-family: "Montserrat";
  transition: background-color 0.2s ease;
}

.job-upload-button:hover {
  background-color: #2d2d2d; /* Darker blue on hover */
}

.upload-icon {
  margin: 0; /* Remove any default margins */
  vertical-align: middle; /* Align the icon with the text */
}

.job-submit-button {
  width: 95%;
  padding: 0.75rem;
  background: #2d2d2d;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
  font-family: "Montserrat";
}

.job-submit-button:hover {
  background: #454545;
}

textarea {
  resize: vertical;
  min-height: 100px;
}
.image-preview-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 3 images per row */
  gap: 15px; /* Space between images */
  margin-top: 15px;
  font-family: Montserrat;
}

.image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 100px;
  font-size: 10px;
}

.image-preview img {
  width: 100%;
  height: auto;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  margin-bottom: 5px;
}

/*Policies Page*/
/* General Container */
.policy-page {
  display: flex;
  flex-direction: column;
}
.terms-container {
  display: flex;
  justify-content: center;
  margin-top: 50px;
  margin-bottom: 50px;
  margin-right: 30px;
  height: 100vh; /* Full viewport height */
}

/* Sidebar */
.sidebar {
  width: 25%;
  display: flex;
  flex-direction: column;
  padding: 0px 0 120px 50px;
  text-align: left;
}

.sidebar-item {
  background: none;
  border: none;
  text-align: left;
  font-size: 16px;
  font-family: Horizon;
  font-weight: bold;
  margin-bottom: 15px;
  margin-top: 15px;
  cursor: pointer;
  padding: 10px;
  color: #a9a8a8;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  transition: background-color 0.3s ease;
}

.sidebar-item.active {
  font-weight: bold;
  color: #2d2d2d;
}
.sidebar-item:hover {
  background-color: #2d2d2d;
  color: white;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  transition: 0.3s ease;
  padding: 20px;
}
/* Divider Line */
.divider {
  width: 1px; /* Line thickness */
  background-color: #a9a8a8; /* Line color */
  height: 100%; /* Ensure it takes the full height of the sidebar */
}
/* Content Section */
.content {
  width: 75%;
  padding: 40px;
  overflow-y: auto;
  padding-right: 120px;
  margin-top: 20px;
  scroll-behavior: smooth; /* Smooth scrolling */
}

.content h2 {
  font-size: 18px;
  margin-bottom: 10px;
  font-family: Horizon;
  text-align: left;
}

.content h3 {
  font-size: 16px;
  margin-bottom: 10px;
  font-family: Montserrat;
}

.content p {
  font-size: 14px;
  margin-bottom: 10px;
  line-height: 1.6;
  font-family: Montserrat;
}

.content ul {
  padding-left: 20px;
  list-style-type: disc;
  font-family: Montserrat;
}

.content ul li {
  margin-bottom: 8px;
  font-family: Montserrat;
}

.content::-webkit-scrollbar {
  width: 8px;
}

.content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 10px;
}

.content::-webkit-scrollbar-thumb {
  background: #6b7b58;
  border-radius: 10px;
}

.content::-webkit-scrollbar-thumb:hover {
  background: #4e5b44;
}

/*Partners Application Page*/
.partner-page {
  display: flex;
  flex-direction: column;
}

.partner-Caption {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80px; /* Default padding for larger screens */
}

.partner-Caption p {
  font-size: 18px;
  font-family: Montserrat;
  font-weight: 200;
  text-align: justify;
  line-height: 1.8;
}

.partners-second-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 80px 60px 80px; /* Adjust padding for smaller screens */
}

.partners-second-section h2 {
  text-align: center;
  padding: 20px;
  font-size: 30px; /* Adjust font size for better scaling */
}

.partners-second-section ul {
  padding: 0;
  list-style: none; /* Remove default list styling */
}

.partners-second-section ul li {
  font-family: Montserrat;
  font-size: 20px;
  line-height: 1.8;
  margin-bottom: 15px;
  padding-left: 30px;
  position: relative;
}

.partners-second-section ul li::before {
  content: "•";
  color: #6a8452; /* Bullet color */
  font-weight: bold;
  display: inline-block;
  width: 15px;
  margin-left: -15px;
}
/* Contact Page */
.Contact-Page {
  position: relative;
  width: 100%;
  min-height: 100vh; /* Minimum full viewport height */
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Content container */
.content-container {
  display: flex;
  flex: 1;
  padding: 5% 10%; /* Responsive padding */
  align-items: center;
  justify-content: center;
  flex-wrap: wrap; /* Ensures elements wrap on smaller screens */
  gap: 2rem;
}

/* Left Caption */
.Contant-caption {
  flex: 1;
  color: #000;
  max-width: 400px;
  padding: 20px;
  text-align: left;
}

.Contant-caption h3 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  font-family: Horizon;
  font-weight: bold;
  text-align: start;
  margin-top: 30px;
}

.Contant-caption p {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #2d2d2d;
  font-family: Montserrat;
}

/* Right Contact Form */
.Contact-form {
  flex: 1;
  background: rgba(217, 216, 216, 0.5);
  backdrop-filter: blur(15px);
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  max-width: 400px; /* Controls form width */
  width: 100%;
}

/* Form Fields */
.Contact-form-content {
  display: flex;
  flex-direction: column;
}

.contact-form-field {
  margin-bottom: 1.5rem;
}

.contact-form-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}

.contact-form-field input,
.contact-form-field textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  font-family: "Montserrat";
}

.contact-submit-btn {
  width: 100%;
  padding: 0.75rem;
  background: #2d2d2d;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.contact-submit-btn:hover {
  background-color: #6b7b58;
}
/*Job Sent Appliaction PopUp*/
/* Popup overlay to cover the entire screen */
.Job-sent-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(15px); /* Stronger blur for the form background */
}

/* Popup container styling */
.Job-sent-popup-container {
  background: #6b7b58; /* Light greenish background */
  border-radius: 10px;
  width: 600px;
  text-align: center;
  padding: 30px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  animation: slide-down 0.3s ease;
}

/* Popup heading */
.Job-sent-popup-container h1 {
  font-size: 20px;
  font-weight: bold;
  color: #ffffff;
  margin: 20px 0;
  text-align: center;
}

/* Popup paragraph */
.Job-sent-popup-container p {
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
  color: #ffffff;
  font-family: Montserrat;
  padding: 0 40px 0 40px;
}

/* Button styling */
.Job-sent-popup-button {
  background: #2d2d2d;
  color: #fff;
  border: none;
  padding: 20px 50px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 20px;
  font-family: Montserrat;
}
.Job-sent-popup-close-icon {
  position: absolute;
  align-items: end;
}
.Job-sent-popup-button:hover {
  background: #171717;
}

.animated-mail-icon {
  font-size: 50px;
  color: #ffffff;
  animation: mailSend 2s ease-in-out;
}

/*Popup window*/
/* Overlay to darken the background */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5); /* Semi-transparent black background */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(15px); /* Optional: Adds a blur effect */
}

/* Popup container styling */
.popup-container {
  position: relative;
  width: 70%;
  max-width: 900px;
  height: 80%;
  background: #efebe843; /* Fully opaque background for the popup */
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  padding: 20px;
}

/* Separate background layer for opacity */
.popup-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(239, 235, 232, 0.7); /* Semi-transparent background */
  z-index: -1; /* Place it behind the content */
  border-radius: 12px; /* Match the container's border radius */
}

/* Close Button */
.popup-close-button {
  position: absolute;
  right: 10px;
  font-size: 30px !important;
  min-width: 40px;
  height: 40px;
  border-radius: 50%;
  color: #2d2d2d;
  border: none;
  cursor: pointer;
  background-color: transparent;
}

.popup-close-button:hover {
  color: #333;
  background-color: transparent;
}

/* Left Side: Profile Form */
.popup-left {
  flex: 2; /* Left section takes up 2 parts of available space */
  padding: 30px;
}
.popup-header {
  display: flex;
  justify-content: space-between; /* Spread title and avatar to edges */
  align-items: center; /* Vertically align items */
  margin-bottom: 20px;
  padding-left: 20px;
  padding-right: 20px;
}
.popup-title {
  font-size: 24px !important;
  font-weight: bold !important;
  font-family: Horizon !important;
  margin: 0;
}

.popup-avatar-section {
  display: flex;
  align-items: center; /* Align the avatar vertically with the title */
  justify-content: flex-end;
}

.popup-avatar {
  width: 70px !important;
  height: 70px !important;
  border: 3px solid #ccc;
  border-radius: 50%;
}

.popup-form {
  display: grid;
  gap: 7px;
  padding: 0px 0px 0px 40px;
}
/* .profile-form-field {
} */

.profile-form-field label {
  padding-bottom: 12px;
  display: block;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}
.popup-form-firstrow {
  display: flex;
  gap: 40px; /* Add space between the input fields */
  margin-bottom: 0px; /* Add space below the row */
}

.popup-form-firstrow input {
  flex: 1; /* Ensure both inputs take up equal width */
  padding: 10px;
  width: 90%;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  outline: none;
}

.popup-form-firstrow input:focus {
  border-color: #6b7b58; /* Change border color on focus */
  box-shadow: 0 0 5px rgba(107, 123, 88, 0.5);
}

.popup-form input,
select {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  color: #2d2d2d;
  background: white;
  font-family: "Montserrat";
}

.popup-form-full-width {
  grid-column: span 2;
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  color: #2d2d2d;
  background: white;
  font-family: "Montserrat";
}

.popup-buttons {
  display: flex;
  gap: 140px; /* Add space between the input fields */
  margin-bottom: 0px; /* Add space below the row */
  padding-left: 20px;
  padding-right: 20px;
}
.profile-popUpForm-btn-save {
  background: #2d2d2d;
  color: #fff;
  border: none;
  padding: 10px 50px 10px 50px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 20px;
  font-family: Montserrat;
}

.profile-popUpForm-btn-save:hover {
  background-color: #6c7c59;
}

.profile-popUpForm-btn-cancel {
  color: #2d2d2d;
  border: 2px solid black;
  background-color: transparent;
  padding: 10px 50px 10px 50px;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  font-size: 14px;
  margin-top: 20px;
  font-family: Montserrat;
}

.profile-popUpForm-btn-cancel:hover {
  background-color: #6c7c59;
  border-color: #6c7c59;
  color: #eae3e4;
}

/* Right Side: Navigation */
.popup-right {
  flex: 1;
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px; /* Reduces space between elements for a tighter alignment */
  margin: auto;
  align-items: flex-start; /* Align content (title and buttons) to the left */
}
/* Style for the selected tab button */
.popup-right-button.selected {
  font-weight: bold;
}

/* Style for the selected tab title */
.popup-title.selected {
  font-weight: bold;
}
.popup-right-title {
  font-size: 18px;
  font-weight: bold;
  color: #2d2d2d;
  margin-bottom: 20px;
  font-family: Horizon;
  align-self: stretch; /* Makes the title span the full width */
}

.popup-right-button {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #666;
  text-transform: none;
  font-size: 16px;
  font-weight: 400;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px 12px; /* Slightly larger padding for better button feel */
  border-radius: 8px;
  width: 100%; /* Ensures all buttons align with the title */
}

.popup-right-button:hover {
  background-color: #6b7b584a;
}

.popup-divider {
  border-left: 1px solid #2d2d2d;
  height: 70%;
  margin: auto;
}
/*Reset Password pop-up*/
.reset-popup-buttons {
  display: flex;
  gap: 140px; /* Add space between the input fields */
  margin-bottom: 0px; /* Add space below the row */
  padding-left: 0px;
}
.reset-popup-form {
  display: grid;
  gap: 20px;
  padding: 10px 40px 20px 40px;
}
.reset-form-field {
  width: 80%;
  margin: auto;
}

.reset-form-field label {
  padding-bottom: 12px;
  padding-top: 10px;
  display: block;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}
.reset-popup-form-firstrow {
  display: flex;
  gap: 40px; /* Add space between the input fields */
  margin-bottom: 0px; /* Add space below the row */
}

.reset-popup-form-firstrow input {
  flex: 1; /* Ensure both inputs take up equal width */
  padding: 10px;
  width: 90%;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  outline: none;
}

.reset-popup-form-firstrow input:focus {
  border-color: #6b7b58; /* Change border color on focus */
  box-shadow: 0 0 5px rgba(107, 123, 88, 0.5);
}

.reset-popup-form input {
  width: 80% !important;
  padding: 0.75rem;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  color: #2d2d2d;
  background: rgb(216, 9, 9);
  font-family: "Montserrat";
}

.reset-popup-form-full-width {
  grid-column: span 2;
}

/* .reset-popup-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: auto;
  padding-top: 40px;
  margin-left: 20px;
} */
.reset-popUpForm-btn-save {
  background: #2d2d2d;
  color: #fff;
  border: none;
  margin: auto;
  padding: 10px 50px 10px 50px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 20px;
  font-family: Montserrat;
}
.reset-popUpForm-btn-save:hover {
  background-color: #6c7c59;
}
.reset-popUpForm-btn-cancel {
  color: #2d2d2d;
  border: 2px solid black;
  background-color: transparent;
  padding: 10px 50px 10px 50px;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  font-size: 14px;
  margin-top: 20px;
  font-family: Montserrat;
}
.reset-popUpForm-btn-cancel:hover {
  background-color: #6c7c59;
}

/*BIlling INfo popup*/

.billing-info-content {
  display: flex;
  flex-direction: column;
  align-items: center; /* Center horizontally */
  justify-content: center; /* Center vertically */
  height: 50vh; /* Full viewport height for vertical centering */
  padding: 20px; /* Add padding for better spacing */
}

.billing-form-field {
  width: 80%;
  margin: 16px 0; /* Add vertical spacing between fields */
}

.billing-form-field label {
  padding-bottom: 8px;
  display: block;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}

.billing-form-field input {
  width: 100%; /* Full width of the form field container */
  padding: 0.75rem;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}
.billing-form-field-row {
  display: flex;
  gap: 20px; /* Space between CVV and Expiry Date fields */
  justify-content: space-between; /* Ensure fields are spaced out evenly */
  width: 80%; /* Match the form width */
  margin-top: 16px;
}

.billing-form-field-cvv,
.billing-expiry-date {
  width: 40%; /* Set width of CVV and Expiry Date fields */
  display: flex;
  flex-direction: column;
}

.billing-form-field-cvv label,
.billing-expiry-date label {
  margin-bottom: 8px; /* Space between label and input */
  font-size: 14px;
  font-family: Montserrat;
}

.billing-form-field-cvv input,
.billing-expiry-date input {
  padding: 8px;
  font-size: 16px;
  border: none;
  border-radius: 4px;
}

/*Shipping popUp Window*/
.shipping-info-content {
  display: flex;
  flex-direction: column;
  align-items: center; /* Center horizontally */
  justify-content: center; /* Center vertically */
  height: 50vh; /* Full viewport height for vertical centering */
  padding: 20px; /* Add padding for better spacing */
}

.form-field {
  width: 80%;
  font-family: "Montserrat";
  margin: auto;
  padding: 15px;
}

.form-field label {
  padding-bottom: 8px;
  display: block;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}

.form-field input {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}
.form-field select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #2d2d2d;
  border-radius: 6px;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}
.shipping-form-field {
  width: 80%;
  font-family: "Montserrat";
  margin: auto;
  padding: 15px;
}

.shipping-form-field label {
  padding-bottom: 8px;
  display: block;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}

.shipping-form-field input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}
.shipping-form-field select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 14px;
  color: #2d2d2d;
  font-family: "Montserrat";
}
.shipping-form-field-row {
  display: flex;
  justify-content: space-between;
  width: 74%;
  margin-top: 16px;
  margin: auto;
}

.Shipping-form-field-postal,
.Shipping-city {
  width: 40%;
  display: flex;
  flex-direction: column;
}

.Shipping-form-field-postal label,
.Shipping-city label {
  margin-bottom: 8px;
  font-size: 14px;
  font-family: Montserrat;
}

.Shipping-form-field-postal input,
.Shipping-city input {
  padding: 8px;
  font-size: 16px;

  border: 1px solid #ccc;
  border-radius: 8px;
  height: 40px;
}

.form-buttons {
  text-align: center;
  align-items: center;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: auto;
  padding: 20px;
}

.submit-btn {
  background: #2d2d2d;
  color: #fff;
  border: none;
  padding: 10px 50px 10px 50px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 20px;
  font-family: Montserrat;
}

.submit-btn:hover {
  background-color: #6c7c59;
}
.addAddress-btn {
  background: #2d2d2d;
  color: #fff;
  border: none;
  padding: 10px 40px 10px 40px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 20px;
  font-family: Montserrat;
}

.addAddress-btn:hover {
  background-color: #6c7c59;
}

/*Orders popup*/
.orders-popup {
  padding: 20px;
  width: 100%;
  max-height: 400px; /* Set the maximum height for the container */
  overflow-y: auto; /* Enable vertical scrolling */
  border-radius: 8px; /* Rounded corners */
}

/* Custom Scrollbar */
.orders-popup::-webkit-scrollbar {
  width: 8px;
}

.orders-popup::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 10px;
}

.orders-popup::-webkit-scrollbar-thumb {
  background: #6b7b58;
  border-radius: 10px;
}

.orders-popup::-webkit-scrollbar-thumb:hover {
  background: #4e5b44;
}
.order-card-popup {
  border: 1px solid #e1e1e1;

  padding: 15px;
  margin-bottom: 20px;
  border-radius: 8px;
  background-color: #fff;
  position: relative; /* No absolute positioning required */
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #6c7c59;
}

.arrow-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #daded6;
}

.arrow-button:hover {
  color: #4e5b44;
  background: none;
}

.order-dropdown {
  margin-top: 10px; /* Adds space between the dropdown and the header */
  background: #e2dedd;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
  border-radius: 8px;
  z-index: 1;
}
/* Order Info Section Style */
.order-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: Montserrat;
  margin-bottom: 15px; /* Space between info and progress bar */
}

.order-info-headers {
  font-size: 16px;
  flex-grow: 1;
}

.order-info h3 {
  margin: 5px 0;
  font-size: 18px;
}

.order-info p {
  margin: 5px 0;
  color: gray;
}

.order-info img {
  width: 60px;
  height: 60px;
  border-radius: 50%; /* Circular Image */
  object-fit: cover; /* Ensures proper image scaling */
}

/* Progress Timeline Container */
.progress-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  position: relative;
  width: 100%;
}

/* Progress Step Styles */
.progress-step {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.3%; /* 3 steps, evenly spaced */
  text-align: center;
  font-family: Montserrat;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #d7e3d2;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4b6b47;
}

.progress-step.completed .step-circle {
  background-color: #4b6b47;
  color: white;
  z-index: 1;
}

.step-label {
  margin-top: 8px;
  font-size: 12px;
  color: #4b6b47;
}

.progress-container::before {
  content: "";
  position: absolute;
  top: 20px; /* Align with the step circles */
  left: 0;
  right: 0;
  height: 4px;
  background-color: #d7e3d2;
}

.progress-step.completed ~ .progress-step::before {
  background-color: #4b6b47;
}

.progress-step:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 20px;
  right: -50%;
  width: 100%;
  height: 4px;
  background-color: #d7e3d2;
}

.progress-step.completed:not(:last-child)::after {
  background-color: #4b6b47;
}

/*My cart Page*/
/* Add to Cart Notification */
.added-to-cart-notification {
  animation: slideIn 0.3s ease-out, fadeOut 0.3s ease-out 2.7s forwards;
}

@keyframes slideIn {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* Cart Overlay Animation */
.Cart-popup,
.overlay-container-vendor {
  animation: slideInCart 0.3s ease-out;
}

@keyframes slideInCart {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Highlight animation for newly added items */
@keyframes highlightItem {
  0% {
    background-color: rgba(107, 123, 88, 0.3);
  }
  100% {
    background-color: transparent;
  }
}
/* General styles */

.Shopping-cart-title h2 {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 30px;
  padding-top: 20px;
  margin-left: 100px;
  color: #2d2d2d;
  text-align: left;
}

/* Flex container for cart body */
.Shopping-cart-body {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  justify-content: space-between;
}

/* Left Side: Table */
.Shopping-cart-table {
  flex: 1;
  padding: 20px 12px 20px 124px;
  border-radius: 12px;
}

.cart-header {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr 0.5fr;
  font-weight: bold;
  color: #555;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.cart-item {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr 0.5fr;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 2px solid #eee;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 15px;
}

.item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}
.item-image-brand {
  width: 80px;
  height: 24px;
}
.item-title {
  font-size: 16px;
  font-weight: bold !important;
  color: #2d2d2d;
  font-family: horizon !important;
}

.item-info {
  font-size: 11px !important;
  color: #777;
}

.unit-price,
.quantity {
  text-align: center;
  font-size: 20px !important;
  color: #444;
  width: 70%;
}

.quantity {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
}

.quantity button {
  min-width: 1px;
  width: 20px; /* Set the desired width */
  height: 20px; /* Set the desired height */
  background-color: #6c7c59;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.3s ease;
  color: #2d2d2d; /* Hide text or icon */
  font-size: 14px; /* Hide font text */
  justify-content: center;
  align-items: center;
  text-align: center;
}

.quantity button:hover {
  background-color: #2d2d2d; /* Darker background on hover */
  color: #eae3e4;
  border-color: #bbb;
}

.price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end; /* Align price and delete icon to the right */
  width: 90%;
}

.price {
  margin-bottom: 10px; /* Space between price and delete icon */
  font-weight: bold !important;
  width: 90%;
}

.delete-icon {
  cursor: pointer;
  font-size: 20px;
  color: #2d2d2d; /* Red color for delete icon */
  transition: color 0.3s ease;
  margin-top: 120px;
}

.delete-icon:hover {
  color: #6c7c59; /* Darker red on hover */
}
/* Table Actions */
.cart-actions {
  display: flex;
  gap: 20px;
  justify-content: flex-end; /* Align buttons horizontally to the right */
  margin-top: 40px;
}

.checkout-button {
  padding: 12px 24px;
  font-size: 10px !important;
  border-radius: 8px !important;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: "Montserrat";
}

.continue-button {
  background-color: #fff !important;
  color: #2d2d2d !important;
  border: 2px solid #2d2d2d !important;
  border-radius: 8px !important;
  width: 20%;
  height: 36px;
  font-size: 10px !important;
  font-family: "Montserrat";
}

.continue-button:hover {
  background-color: #f5f5f5;
  border-color: #bbb;
}

.checkout-button {
  background-color: #2d2d2d !important;
  color: #eae3e4 !important;
  width: 20%;
  font-size: 14px;
  height: 36px;
  font-family: Montserrat;
}

.checkout-button:hover {
  background-color: #45a045;
}

/* Right Side: Summary */
.Shopping-cart-bill-section {
  flex: 1;
  max-width: 385px;
  height: 500px !important;
  padding: 50px;
}

.cart-summary {
  padding: 25px;
  background-image: linear-gradient(#6c7c59, #2d2d2d);
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.summary-title {
  font-size: 20px;
  font-weight: bold !important;
  margin-bottom: 20px !important;
  color: #eae3e4;
  font-family: Horizon !important;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-top: 2px solid #fff;
  font-size: 16px;
  color: #eae3e4;
}

.summary-item.total {
  font-weight: bold;
  font-size: 18px;
  margin-top: 20px;
  font-family: Horizon;
}

/*Payment Icons border*/
.payment-icons {
  display: flex;
  justify-content: center;
  gap: 15px;
  border-radius: 8px;
  margin-top: 20px;
  background-color: #fff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 12px;
}

.payment-icons img {
  width: 50px;
  height: 35px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.payment-icons img:hover {
  transform: scale(1.1);
}

/*ProductPAge */

/* Main Container */
.product-page {
  margin: 0 auto;
}

/* Grid Layout */
.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 120px;
  padding: 120px 170px 120px 180px;
}

/* Product Image Section */
.product-image-container {
  position: relative;
}

.product-main-image {
  width: 100%;
  border-radius: 8px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  border: 1px solid #ddd;
  height: 400px;
}

.thumbnail-container {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin-top: 10px;
}

.thumbnail-image {
  width: 80px; /* Set a fixed width */
  height: 80px; /* Set a fixed height */
  object-fit: cover; /* Ensures the thumbnail covers the area without stretching */
  margin: 5px;
  cursor: pointer;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  border: 1px solid #ddd;
  transition: transform 0.3s ease-in-out; /* Hover effect */
}

.thumbnail-image:hover {
  transform: scale(1.1); /* Slight zoom effect on hover */
}
/* Product Details Section */
.product-details {
  flex-direction: column;
  font-family: Montserrat;
}
.product-brand {
  font-size: 18px;
  font-weight: normal;
  font-family: Montserrat;
  color: #777;
}
.product-title {
  font-size: 24px;
  font-weight: bold;
  text-align: left;
  font-family: Horizon;
  margin-bottom: 4px;
  color: #2d2d2d;
}

.product-rating {
  color: #777;
  margin-bottom: 10px;
  font-family: Montserrat;
}

.product-price {
  font-size: 24px !important;
  font-weight: bold;
  color: #2d2d2d;
  margin-bottom: 20px;
}
/* Color Selector Section */
.color-selector {
  align-items: center;
  font-family: Montserrat;
  padding-bottom: 13px;
}

.color-selector-label {
  font-size: 12px;
  margin-right: 10px;
  padding-bottom: 12px;
  font-family: Montserrat;
}

.color-options {
  display: flex;
  gap: 10px;
  padding-top: 15px;
}

.color-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.3s;
  font-family: Montserrat;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.color-circle:hover {
  border-color: #4b6b47;
  transform: scale(1.1);
}

.color-circle.selected {
  border: 2px solid #4b6b47 !important;
  transform: scale(1.1);
  border-color: #4b6b47;
  font-family: Montserrat;
}
.color-name-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: #333;
  text-transform: uppercase;
}

.selected-color-text {
  font-family: Montserrat;
  font-size: 14px;
  margin: 8px 0px;
  color: #2d2d2d;
}
/* Tooltip for color names */
.color-circle::after {
  content: attr(title);
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%);
  padding: 5px 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
  z-index: 10;
}

.color-circle:hover::after {
  visibility: visible;
  opacity: 1;
}
/* Size Selector Section */
.size-selector {
  align-items: center;
  font-family: Montserrat;
  padding-bottom: 13px;
}

.size-selector-label {
  font-size: 12px;
  margin-right: 10px;
  padding-bottom: 12px;
  font-family: Montserrat;
}
.size-options {
  display: flex;
  gap: 10px;
}

.size-button {
  padding: 10px 20px;
  border: 2px solid #ffffff;
  background-color: #2d2d2d;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  font-weight: normal;
  color: #ffffff;
  transition: all 0.3s;
  font-family: Montserrat;
}

.size-button:hover {
  border-color: #4b6b47;
  background-color: #f9f9f9;
  color: #2d2d2d;
}

.size-button.selected {
  border-color: #4b6b47;
  background-color: #6c7c59;
  color: white;
}
.bimcad-button {
  padding: 10px 20px;
  background-color: transparent;
  margin: 20px;
  color: #2d2d2d;
  border: 1px solid #2d2d2d;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: background-color 0.3s;
}

.bimcad-button:hover {
  background-color: #2d2d2d;
  color: #eae3e4;
}

.action-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  justify-content: space-around;
  flex-direction: row;
  align-items: baseline;
  transition: all 0.3s;
}

.action-button {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}
.action-dropdown {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 10px;
  width: 50%;
  align-items: flex-end;
}
.action-dropdown button {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  background-color: white;
  color: #2d2d2d;
  border: none;
  width: 100%;
}
.button-primary {
  background-color: #2d2d2d;
  color: white;
  border: none;
  width: 100%;
}
.button-primary:hover {
  background-color: #6c7c59;
}
.button-secondary {
  background-color: white;
  color: #2d2d2d;
  border: 1px solid #2d2d2d;
  width: 100%;
}
.button-secondary:hover {
  background-color: #6c7c59;
  border: 1px solid #6c7c59;
  color: #ffffff;
}
/*Request Info Form popUp*/
/* Overlay for background blur */
.confirmation-message {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 70%; /* Ensure it takes full height */
  text-align: center;

  padding: 20px;
}
.confirmation-message h3 {
  font-size: 24px;
  margin-bottom: 10px;
  font-family: Montserrat;
  color: #fff;
  padding: 0px 30px 0px 30px;
}
.confirmation-message button {
  font-size: 16px;
  margin-bottom: 20px;
  font-family: Montserrat;
  color: #eae3e4;
  background-color: #6c7c59;
}
.confirmation-message button:hover {
  background-color: #2d2d2d;
}
.requestInfo-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Popup container */
.requestInfo-popup {
  /* background: rgba(255, 255, 255, 0.2); semi-transparent */
  background-color: white;
  backdrop-filter: blur(15px); /* glassy blur effect */
  -webkit-backdrop-filter: blur(15px); /* Safari support */
  border-radius: 16px;
  width: 800px;
  height: 610px;
  padding: 20px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37),
    /* soft big glow */ 0 4px 6px rgba(0, 0, 0, 0.1); /* gentle base shadow */
  border: 1px solid rgba(255, 255, 255, 0.18); /* subtle border for glass */
  position: relative;
  overflow-y: hidden;
  color: #2d2d2d; /* ensure text is visible on the transparent background */
}

/* Header */
.requestInfo-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  margin: 20px;
}

.requestInfo-popup-header h2 {
  font-size: 20px;
  margin: 0;
  font-family: "Horizon";
  color: #2d2d2d;
}

.requestInfo-close-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #2d2d2d;
}
.requestInfo-brand-user-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
}
.requestInfo-brand {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
  font-family: Montserrat;
}
.requestInfo-brand-name p {
  margin: 0;
  font-size: 13px;
  font-family: montserrat;
  margin-bottom: 8px;
  color: #2d2d2d;
}
.requestInfo-brand-name h2 {
  margin: 0;
  font-size: 20px;
  font-family: "Horizon";
  color: #2d2d2d;
}
/* Brand info */
.requestInfo-brand-info {
  text-align: center;
  margin-bottom: 20px;
  width: 100px;
  border-radius: 8px;
  height: 100px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.requestInfo-brand-logo {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.requestInfo-brand-info h2 {
  margin: 5px 0;
}

.requestInfo-brand-info h3 {
  margin: 5px 0;
  color: gray;
}

/* User info */
.requestInfo-user-info {
  text-align: right;
  font-size: 14px;
  margin-bottom: 20px;
  color: #2d2d2d;
}

/* Form */
.requestInfo-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.requestInfo-form-group {
  display: flex;
  flex-direction: column;
  color: #fff;
}
.requestInfo-form .required-field {
  color: #f44336;
  margin-left: 4px;
}
.requestInfo-form-group textarea {
  padding: 10px;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #efeded;
}

.requestInfo-form-group label {
  font-weight: bold;
  margin-bottom: 5px;
}

.requestInfo-input-group {
  display: flex;
  gap: 10px;
}

.requestInfo-input-group select,
textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  flex: 1;
  background-color: #efeded;
  cursor: pointer;
  width: 97%;
}
.requestInfo-input-group input {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  flex: 1;
  background-color: #ffffff;
}
.requestInfo-input-group input:disabled {
  background-color: #f0f0f0;
  color: #888;
  border: 1px solid #ddd;
}
textarea {
  resize: none;
  width: 97%;
  height: 10px;
}

.requestInfo-submit-button {
  padding: 10px 20px;
  background-color: #6c7b58;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  width: 30%;
  margin: auto;
  margin-top: 15px;
}

.requestInfo-submit-button:hover {
  background-color: #5a684c;
}
/* RequestInfo form validation styling */
.requestInfo-form .required-field {
  color: #f44336;
  margin-left: 4px;
}

.requestInfo-form .input-error {
  border: 1px solid #f44336 !important;
  background-color: rgba(244, 67, 54, 0.05) !important;
}

.requestInfo-form .error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
  text-align: left;
  animation: fadeIn 0.3s ease;
}

.requestInfo-form .button-disabled {
  background-color: #cccccc !important;
  cursor: not-allowed !important;
  opacity: 0.7;
}

/* Animation for error messages */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Improve the select and input styling */
.requestInfo-input-group select,
.requestInfo-input-group input {
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.requestInfo-input-group select:focus,
.requestInfo-input-group input:focus {
  border-color: #6b7b58;
  outline: none;
}

/* Improve submit button styling */
.requestInfo-submit-button {
  transition: background-color 0.3s ease, opacity 0.3s ease;
}

.requestInfo-submit-button:hover:not(:disabled) {
  background-color: #5a6a47;
}
/* Popup.css */
.option-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.option-popup-container {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 300px;
  text-align: center;
}

.option-popup-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 30px;
}

.option-popup-card {
  background-color: #f0f0f0;
  padding: 20px;
  border-radius: 8px;
  cursor: pointer;
  width: 45%;
  text-align: center;
}

.option-popup-card:hover {
  background-color: #ddd;
}

.option-popup-close-button {
  background-color: #007bff;
  color: white;
  border-radius: 5px;
  padding: 10px 20px;
}

/* Modal styling */
.product-page.blurred {
  filter: blur(8px);
  pointer-events: none;
  user-select: none;
}
/* Blur background */
.product-page.blurred {
  filter: blur(8px);
  pointer-events: none;
  user-select: none;
}

/* Modal styling */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #eae3e411;
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transform: scale(0.9); /* Slightly shrink modal initially */
  transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out; /* Combined ease-in-out transitions */
}
/* Thumbnail images */

.modal-content {
  max-width: 100%; /* Limits the size of the modal */
  max-height: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
/* Modal Image */
.modal-content img {
  width: 100%;
  height: auto;
  object-fit: cover; /* Ensures images cover the area without distortion */
  border-radius: 8px;
}
.modal.opening {
  opacity: 1;
  transform: scale(1); /* Full size when opening */
}

.modal.closing {
  opacity: 0;
  transform: scale(0.9); /* Full size when opening */
}

.modal-image {
  max-width: 100%;
  max-height: 100%;
}

.modal-close,
.modal-prev,
.modal-next {
  position: absolute;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 10px;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.3s ease-in-out; /* Button hover effect */
}

.modal-close {
  top: 0px;
  right: 16px;
  background-color: transparent;
  color: #2d2d2d;
}

.modal-prev {
  left: 10px;
  background-color: transparent;
  color: #2d2d2d;
}
.modal-prev:hover,
.modal-next:hover,
.modal-close:hover {
  background-color: transparent;
  color: #2d2d2d;
}

.modal-next {
  right: 10px;
  background-color: transparent;
  color: #2d2d2d;
}

/* Collapsible Section */

/* Collapsible Sections Container */
.collapsible-container {
  display: flex;
  flex-direction: column; /* Stack collapsible sections vertically */
  align-items: flex-start; /* Align sections to the left */
  margin-top: 20px; /* Add space between image and sections */
  width: 60%; /* Occupy full container width */
}

/* Individual Collapsible Section */
.collapsible-section {
  margin-bottom: 15px; /* Add spacing between sections */
  width: 70%; /* Allow sections to take full width */
  border-top: 2px solid #eaeaea; /* Optional: Separator between sections */
  font-family: Montserrat;
  margin-left: 150px;
  font-size: 30px !important;
}

.collapsible-header {
  font-size: 20px;
  font-weight: bold;
  font-family: Horizon;
  color: #2d2d2d;
  cursor: pointer;
  padding: 10px 0;
  font-family: Horizon;
  justify-content: space-between;
  display: flex;
}

.collapsible-content {
  font-size: 12px;
  color: #4b4b4b;
  line-height: 1.5;
  padding: 5px 0;
  display: none; /* Initially hidden */
}
.product-contents {
  font-size: 14px;
  color: #4b4b4b;
  line-height: 1.5;
  padding: 5px 0;
}
.product-contents h5 {
  font-size: 18px;
}
.product-contents img,
iframe {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.collapsible-icon {
  transition: transform 0.3s ease; /* Smooth rotation */
}

.collapsible-icon.rotated {
  transform: rotate(180deg); /* Rotate the arrow */
}

.collapsible-section.open .collapsible-content {
  display: block; /* Display content when the section is expanded */
}
.Product-overview-title {
  font-family: Montserrat;
  font-weight: bold;
}
.product-details p {
  font-size: 14px;
}

.product-details .label {
  margin-right: 5px; /* Space between the word before ':' and the text after */
  font-weight: bold; /* Optional: makes the label bold */
}
.BIMCAD-Btn {
  background-color: transparent;
  border: 1px solid #2d2d2d;
  color: #2d2d2d;
  border-radius: 15px;
  width: 40%;
  text-align: center;
}
.span-container {
  display: flex;
  gap: 10px; /* Space between each span */
  flex-wrap: wrap; /* Allow items to wrap if space is tight */
}

.span-container span {
  padding: 5px 10px;
  border: 2px solid #4b6b47; /* Border color */
  border-radius: 20px; /* Round the corners */
  text-decoration: underline; /* Underline the text */
  font-size: 14px;
  font-family: Montserrat; /* Optional: Apply a specific font */
  color: #2d2d2d; /* Text color */
  transition: all 0.3s; /* Smooth transition for hover effects */
}

.span-container span:hover {
  background-color: #f9f9f9; /* Change background color on hover */
  border-color: #6c7c59; /* Change border color on hover */
  color: #4b6b47; /* Change text color on hover */
}
.page-container {
  display: flex;
  flex-direction: row; /* Arrange items in a row */
  align-items: flex-start; /* Align items at the top */
  justify-content: space-between; /* Space between collapsible sections and right-side div */
  gap: 20px; /* Add space between the two sections */
}

.right-side-content {
  width: 50%; /* Occupy 35% of the page */
  padding: 5px 20px;
  font-family: Montserrat;
  color: #2d2d2d;
  margin-top: -20px;
}
.Products-Materials {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}
.Products-Materials h4 {
  font-size: 24px;
}
.Products-Materials ul li {
  font-size: 20px;
  font-family: Montserrat;
  color: #2d2d2d;
  text-align: left;
  font-weight: 400;
}
.Products-Materials span {
  color: #777;
  font-size: 12px;
}
.Product-info {
  display: flex;
  flex-direction: column;
}
.Product-info h4 {
  font-size: 24px;
}
.Product-info span {
  color: #777;
  margin: -20px 0;
  font-size: 12px;
  padding-right: 120px;
}

/*Materail Section info*/
/* Container for the entire collapsible section */
.material-collapsible-container {
  font-family: Montserrat;
  padding: 20px;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  box-sizing: border-box;
  margin-left: -26px;
}

/* Title styling */
.material-collapsible-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #000;
}

/* Description styling */
.material-collapsible-description {
  font-size: 14px;
  color: #6c6c6c;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* Box containing all sections */
.material-collapsible-box {
  border-radius: 8px;
  overflow: hidden;
}

/* Individual section styling */
.material-collapsible-section {
  border-bottom: 1px solid #ddd;
}

.material-collapsible-section:last-child {
  border-bottom: none;
}

/* Header of each section */
.material-collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  color: #000;
  background-color: #fff;
  transition: background-color 0.3s ease;
}

.material-collapsible-header:hover {
  background-color: #f9f9f9;
}

/* Plus/Minus icon */
.material-collapsible-icon {
  font-size: 18px;
  font-weight: bold;
}

/* Hidden content styling */
.material-collapsible-content {
  padding: 15px 20px;
  font-size: 14px;
  color: #4b4b4b;
  background-color: #f9f9f9;
  line-height: 1.5;
}

/* Reviews Section */
.reviews-section {
  margin-top: 10rem;
  margin: 0 auto;
  width: 100%;
  padding-left: 10rem;
  display: flex;
  flex-direction: column;
}
.reviews-section h2 {
  font-family: Horizon;
  font-size: 25px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: left;
  color: #2d2d2d;
}
.review-card {
  width: 90%;
  padding: 15px;
  background-color: #ebe4e5;
  border-radius: 8px;
  margin-bottom: 20px;
  font-family: Montserrat;
}
.review-subtitle {
  display: flex;
  justify-content: space-between;
}
.review-subtitle p {
  font-family: Montserrat;
  color: #2d2d2d;
  font-size: smaller;
  margin-bottom: 10px;
}
.review-Summary {
  font-family: Montserrat;
  color: #2d2d2d;
  font-size: smaller;
  margin-bottom: 2rem;
}
.review-title {
  font-family: Horizon;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.review-rating {
  font-family: Montserrat;
  color: #666;
  margin-bottom: 10px;
}
.review-subtext {
  font-family: Montserrat;
  color: #2d2d2d;
  margin-bottom: 10px;
}
.review-text {
  font-size: 14px;
  font-family: Montserrat;
  color: #777;
}
.reviews-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 2rem;
  padding-right: 177px;
}

.write-review-btn {
  background-color: #2d2d2d;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

/* Password strength indicator styles */
.password-strength-container {
  margin-top: 8px;
}

.password-strength-bars {
  display: flex;
  gap: 4px;
}

.password-strength-bar {
  flex: 1;
  height: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.password-strength-label {
  text-align: right;
  font-size: 12px;
  margin-top: 4px;
}

/* Password requirements popup styles for right-side positioning */
.password-requirements-popup-right {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  width: 250px;
  position: absolute;
  z-index: 1000;
  margin-left: 8px;
}

.password-requirements-popup-right::before {
  content: "";
  position: absolute;
  top: 20px;
  left: -10px;
  border-width: 10px 10px 10px 0;
  border-style: solid;
  border-color: transparent white transparent transparent;
}

.password-requirements-title {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 14px;
}

.password-requirements-list {
  margin: 0;
  padding-left: 20px;
}

.password-requirement-item {
  margin-bottom: 4px;
  font-size: 13px;
  transition: color 0.3s ease;
}

.requirement-met {
  color: green;
}

.requirement-not-met {
  color: red;

  transition: background-color 0.2s;
}
.write-review-btn:hover {
  background-color: #1a1a1a;
}

.review-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.review-form-container {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  width: 90%;
  max-width: 500px;
  position: relative;
}

.close-form-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  color: #2d2d2d;
  border: none;
  cursor: pointer;
  font-size: 1.5rem;
}
.close-form-btn:hover {
  color: #2d2d2d;
  background-color: none;
}
.review-input {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
  font-size: 1rem;
}

.review-textarea {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
  font-size: 1rem;
  resize: vertical;
}
.submit-review-btn {
  background-color: #2d2d2d;
  color: white;
  padding: 0.75rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.submit-review-btn:hover {
  background-color: #1a1a1a;
}
.star-rating {
  display: flex;
  gap: 0.25rem;
}
.no-reviews {
  margin: auto;
  text-align: center;
  font-family: Montserrat;
  color: #ccc;
  font-weight: bold;
  font-size: larger;
  margin-bottom: 2rem;
}
.related-products-container {
  max-width: 100%;
  padding: 48px 16px;
  text-decoration: none;
  color: #2d2d2d;
}

.related-products h2 {
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: 0.05em;
  margin-bottom: 32px;
}

.related-product-card {
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  width: 100%;
}
.related-product-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  width: 100%;
}
.no-related-products {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  color: #555;
}
.related-img {
  width: 100%;
  height: 256px;
  object-fit: cover;
  transition: transform 0.3s ease;
  border-radius: 10px;
}
.related-product-card:hover .related-img {
  transform: scale(1.05);
}
.related-info {
  padding: 10px;
  width: 100%;
}
.related-category {
  font-size: 14px;
  color: gray;
}

.related-name {
  font-size: 18px;
  font-weight: bold;
}

.related-description {
  font-size: 14px;
  color: gray;
}

.related-price {
  font-size: 16px;
  font-weight: bold;
  margin-top: 5px;
}
/* Swiper customization */
.swiper-button-next,
.swiper-button-prev {
  color: #000 !important;
  background: white;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 20px !important;
}

.swiper-button-disabled {
  opacity: 0.5 !important;
}
/*brand carsoul*/
.carousel-container {
  max-width: 400px;
  margin: 10px;
  background: white;
  padding: 16px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 500px;
  font-family: "Montserrat";
  perspective: 1000px;
  margin-left: 70px;

  align-items: center;
}
.carousel-header {
  text-align: center;
}
.carousel-divider {
  border-top: 1px solid #2d2d2d;
  margin: 10px 0;
}
.carousel-contact-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 12px;
  color: #2d2d2d;
}
.carousel-contact-section a {
  color: #2d2d2d;
  text-decoration: none;
  font-family: "Montserrat";
  font-size: 14px;
}

.carousel-contact-section a:hover {
  background-color: white;
}

.contact-link {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.carousel {
  position: relative;
  margin-top: 16px;
  overflow: hidden;
  height: 300px;
  align-items: center;
  display: flex;
  flex-direction: column;
}

.carousel-product-image {
  width: 176px;
  height: 162px;
  object-fit: cover;
  border-radius: 8px;
}

.carousel-product-info {
  text-align: center;
  color: #333;
  font-family: "Montserrat";
}

.carousel-product-details {
  font-size: 12px;
  color: #777;
  font-family: "Montserrat";
}

.carousel-product-name {
  font-size: 16px;
  font-weight: bold;
  font-family: "Montserrat";
  padding: 0px 10px;
}

.carousel-product-price {
  font-size: 14px;
  font-weight: 600;
  color: #2d2d2d;
  font-family: "Montserrat";
}

.carousel-button {
  position: absolute;
  top: 100%;
  transform: translateY(698%);
  background: white;
  border: none;
  padding: 10px;
  border-radius: 42%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  width: auto;
  height: 35px;
  color: #2d2d2d;
  margin: auto;
  z-index: 1000;
}

.carousel-button:hover {
  background: white;
}

.carousel-button.left {
  left: 137px;
}

.carousel-button.right {
  right: -70px;
}

.carousel-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 40%;
  transform-style: preserve-3d;
  transition: transform 0.5s ease;
  margin-top: 32px;
}

.carousel-item {
  position: absolute;
  width: 215px;
  height: 230px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  text-align: center;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.carousel-product-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 10px 10px 0 0;
}
/*careers page*/
.careers-page {
  font-family: Montserrat;
}
.ourCoreValues-section-box {
  padding-top: 100px;
  padding-bottom: 150px;
}
.ourCoreValues-section {
  align-items: center;
  text-align: center;
  color: #6b7b58;
  font-family: "Horizon";
  font-weight: bold;
}
/* General Grid Styles */
.values-grid {
  text-align: center;
  margin: auto;
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
  width: 70%;
}

/* Individual Card Styles */
.value-card {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  text-align: center;
  border: 1px solid #e0e0e0;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 6px 6px 6px 6px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s, box-shadow 0.2s;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
.value-card-content {
  text-align: left;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
/* Icon Styles */
.value-card-icon {
  font-size: 36px; /* Adjust based on actual icons used */
  margin-bottom: 50px;
  color: #333;
}

/* Title Styles */
.value-card-title {
  font-size: 18px;
  font-weight: bold;
  color: #2d2d2d;
  margin-bottom: 10px;
}

/* Description Styles */
.value-card-description {
  font-family: Montserrat;
  padding-left: 0px;
  padding-right: 100px;
  font-size: 12px;
  font-weight: 500;
  color: #2d2d2d;
  line-height: 1.5;
}
/* Container Styles */
.career-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  margin: 0 auto;
  font-family: Montserrat;
}

/* Header Styles */
.career-header {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  color: #2b2b2b;
  background-color: red;
  margin-bottom: 20px;
}

/* Tabs Container */
.career-tabs {
  border-bottom: 1px solid #ddd; /* Bottom border for alignment */
  margin-bottom: 20px;
}

/* Individual Tab Styles */
.tab {
  display: inline-flex; /* Ensure each tab only takes as much space as its content */
  padding: 10px 20px;
  font-size: 14px;
  font-weight: bold;
  border: none;
  background-color: transparent;
  cursor: pointer;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
  color: #999;
  border-radius: 8px 8px 0 0; /* Rounded corners on top */
}

.tab.active {
  color: #000;
  background-color: #f5f5f5; /* Add background color for active tab */
  border-bottom: 2px solid #000; /* Highlight bottom border for active tab */
}

.tab:hover {
  color: #555;
  background-color: #eaeaea; /* Slight hover effect */
}

/* Job List Styles */
.job-list {
  margin-top: 20px;
}

.job-card {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.job-title {
  font-size: 18px;
  font-weight: bold;
  color: #2b2b2b;
  font-family: Horizon;
  margin: 0;
}

.job-tags {
  margin: 10px 0;
}

.job-tag {
  display: inline-block;
  padding: 5px 10px;
  margin-right: 5px;
  background-color: #f5f5f5;
  color: #555;
  font-size: 12px;
  border-radius: 5px;
}

.job-description {
  font-size: 14px;
  color: #666;
  margin: 10px 0;
}

.job-date {
  font-size: 12px;
  color: #999;
  margin: 0;
}

/*Track Order*/
/* OrderPage.css */
.orders-container {
  display: flex;
  align-items: center;
}
.divider-track {
  margin-top: 50px;
  width: 1px; /* Line thickness */
  background-color: #a9a8a8; /* Line color */
  height: 100%; /* Ensure it takes the full height of the sidebar */
}
.sidebar-track {
  width: 25%;
  display: flex;
  flex-direction: column;
  padding: 120px 0 120px 80px;
}
.order-details {
  width: 80%;
  margin-left: 50px;
  margin-top: 50px;
}
.content {
  width: 65%;
  padding: 20px 120px 20px 100px;
  overflow-y: auto;
}

.order-card {
  background: #fff;
  padding: 20px 40px;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px 6px rgba(0, 0, 0, 0.1);
}

.status {
  font-size: 0.9rem;
  padding: 5px 10px;
  border-radius: 5px;
  display: inline-block;
  font-family: Montserrat;
}
.middle-pay-table {
  color: #777;
  text-align: left;
}
.order-pays-subtotal {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.paid {
  background-color: #def9bf;
  color: #6b7b58;
}

.shipped {
  background-color: #def9bf;
  color: #6b7b58;
}

.total {
  font-weight: bold;
}

.item {
  display: flex;
  align-items: center;
  gap: 20px;
}

.item img {
  width: 80px;
  height: 80px;
  border-radius: 10px;
}

.item-details {
  display: flex;
  flex-direction: row;
  gap: 40px;
}

.item-price {
  font-weight: bold;
  width: 10%;
}

.actions {
  display: flex;
  flex-direction: row;
  gap: 20px;
  margin-top: 20px;
  justify-content: flex-end;
}

.cancel-btn,
.reorder-btn {
  padding: 10px 50px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.cancel-btn {
  background-color: white;
  border: 1px solid #2d2d2d;
  color: #2d2d2d;
}
.cancel-btn:hover {
  background-color: #6c7c59;
  color: white;
}
.reorder-btn {
  background-color: #2d2d2d;
  color: white;
}
.progress-container-track {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 500px;
}

/* Progress Step Styles */
.progress-step-track {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.3%; /* 3 steps, evenly spaced */
  text-align: center;
  font-family: Montserrat;
}

.step-circle-track {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4b6b47;
  border: 1px solid #4b6b47;
  z-index: 999;
}
.step-circle-track.completed {
  background-color: #4b6b47; /* Green background for completed steps */
  color: #ffffff; /* White icon color */
  border: 1px solid #4b6b47; /* Match the background */
}
.progress-step-track .completed .step-circle {
  background-color: #4b6b47;
  color: white;
  z-index: 1;
}

.step-label-track {
  margin-top: 8px;
  font-size: 12px;
  color: #4b6b47;
  z-index: 1;
}

.progress-container-track::before {
  position: absolute;
  top: 20px; /* Align with the step circles */
  left: 0;
  right: 0;
  height: 4px;
  background-color: #ffff;
  z-index: 1;
}

.progress-step-track.completed ~ .progress-step::before {
  background-color: #4b6b47;
}

.progress-step-track:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 20px;
  right: -50%;
  width: 100%;
  height: 4px;
  background-color: #d7e3d2;
  z-index: 1;
}

.progress-step-track.completed:not(:last-child)::after {
  background-color: #4b6b47;
}
.star-rating {
  display: flex;
  gap: 5px;
}

.star-rating span {
  font-size: 24px;
  cursor: pointer; /* Optional if you want interactivity */
  transition: transform 0.2s;
}

.star-rating span:hover {
  transform: scale(1.2); /* Enlarge on hover */
}

/*profile side account page*/
/* .Profile-side {
} */

.Profile-side h1 {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
  text-align: left;
  font-family: Horizon;
}

.profile-info {
  width: 100%;
  max-width: 800px;
  margin: auto;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 15px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  font-family: Montserrat;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.profile-info-first p {
  font-size: 16px;
  color: #2d2d2d;
  margin: 5px 0;
  font-weight: bold;
  line-height: 2.5;
}
.billing-label {
  font-size: 16px;
  color: #2d2d2d;
  margin: 5px 0;
  font-weight: bold;
  line-height: 2.5;
}
.profile-info-first p:nth-child(odd) {
  color: #777;
}

.popup-avatar-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.profile-info-second {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-top: 1px solid #ececec;
  width: 100%;
  max-width: 800px;
  margin: auto;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 15px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  font-family: Montserrat;
}

.profile-info-second-left,
.profile-info-second-right {
  width: 48%;
}

.profile-info-second-left p,
.profile-info-second-right p {
  font-size: 16px;
  color: #2d2d2d;
  margin: 5px 0;
  font-weight: bold;
  line-height: 2.5;
}

.profile-info-second-left p:nth-child(odd),
.profile-info-second-right p:nth-child(odd) {
  color: #777;
}
.saved-cards {
  overflow-y: auto;
  max-height: 300px;
  padding: 10px 0;
  width: 100%;
  max-width: 800px;
  margin: auto;
  padding: 10px;
  border-radius: 15px;
  font-family: Montserrat;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.saved-cards button {
  background-color: #fff;
  color: #6c7c59;
  border-radius: 4px;
  text-align: center;
  font-family: Montserrat;
  font-size: 12px;
  width: 100%;
  margin: auto;
}
.AddnewPayment Button {
  background-color: #fff;
  color: #6c7c59;
  border-radius: 4px;
  text-align: center;
  font-family: Montserrat;
  font-size: 12px;
  width: 100%;
  margin: auto;
}
.saved-cards p {
  font-family: Montserrat;
  font-size: 14px;
  color: #fff;
  text-align: center;
}
/* Custom Scrollbar */
.saved-cards::-webkit-scrollbar {
  width: 8px;
}

.saved-cards::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 10px;
}

.saved-cards::-webkit-scrollbar-thumb {
  background: #6b7b58;
  border-radius: 10px;
}

.saved-cards::-webkit-scrollbar-thumb:hover {
  background: #4e5b44;
}

/* Cards.css */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 20px;
  padding: 20px;
  background-color: #f5f5f5; /* Optional, for better visuals */
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  text-align: center;
  padding: 15px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
}

.card-image {
  width: 100px;
  height: auto;
  object-fit: contain;
}

.card-name {
  margin-top: 10px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/*Signup form*/
.signup-logo {
  font-weight: bold;
  text-align: center;
  margin: auto;
  padding-top: 20px;
  padding-bottom: 20px;
}
.signup-logo img {
  width: 50%;
  height: 50%;
  text-align: center;
}

.signup-form {
  width: 300px;
  text-align: center;
  padding-bottom: 130px;
}
.register-policy {
  margin-top: 8px;
  font-size: 14px;
  font-family: Montserrat;
  font-weight: 400;
  text-align: center;
}
.register-policy a {
  color: #efebe8;
  text-decoration: none;
  font-weight: bold;
}

/* vondor profile page */
.vendorprofile-card {
  height: 15rem;
  width: 15rem;
  display: flex;
  flex-direction: column;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  overflow: hidden;
  margin-left: 5rem;
  margin-top: 2rem;
}

.vendorprofile-card-media {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.vendorprofile-card-content {
  background-color: #eae3e4;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.vendorprofile-card-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  text-align: left;
  font-size: 0.8rem;
  font-family: "Montserrat";
  padding: 20px;
}

.vendorprofile-card-description {
  font-size: 12px;
  color: gray; /* Use appropriate color as per theme */
  margin-bottom: 8px;
}

.vendorprofile-card-price {
  font-weight: bold;
  font-size: 16px;
  color: black; /* Use appropriate color as per theme */
}

.vendorcategories-grid-container {
  padding: 39px;
}

.vendorcategories-grid-container h1 {
  font-family: "Horizon";
  font-weight: bold;
  font-size: 20px;
  margin-left: 2px;
  text-align: left;
}

.vendorcategories-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24px; /* Adjust spacing between grid items */
}

.vendorprofile-card-title {
  font-family: "Montserrat";
  text-align: center;
  font-size: 10px;
}

.Productsgrid-header {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  align-items: center;
  margin-right: 1rem;
}

.Productsgrid-header button {
  height: 1.5rem;
  background-color: white;
  color: black;
  box-shadow: none;
  border-radius: 20px;
  border: 1px solid black;
}

/* Card container */
.vendorprofile-products-card {
  display: flex;
  flex-direction: column; /* Stack children vertically */
  align-items: center; /* Center content horizontally */
  width: 15rem; /* Adjust width */
  height: 350px;
  background-color: white; /* Card background */
  border-radius: 10px; /* Slightly rounded corners */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */
  transition: transform 0.2s ease, box-shadow 0.2s ease; /* Smooth hover effect */
}

.vendorprofile-products-card:hover {
  transform: translateY(-5px); /* Slight lift on hover */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15); /* Enhanced shadow on hover */
}

/* Product image */
.vendorprofile-products-card-media {
  width: 100%; /* Full width of the card */
  height: 250px; /* Fixed height for uniformity */
  object-fit: cover; /* Ensure image scales properly */
  border-radius: 8px; /* Slightly round image corners */
  margin-bottom: 0.2rem; /* Space below the image */
}
.vendorprofile-products-card-content {
  text-align: left;
  margin-left: -60px;
}

.vendorprofile-products-card-content p {
  font-family: "Montserrat";
}

/* Product title */
.vendorprofile-products-card-title {
  font-size: 0.9rem; /* Title font size */
  text-align: left;
  font-weight: bold; /* Bold title */
  text-align: left; /* Center-align title */
  color: #333; /* Darker text color for title */
  margin-bottom: 0.5rem; /* Space below title */
  font-family: "Montserrat"; /* Consistent font */
}

/* Product description */
.vendorprofile-products-card-description {
  font-size: 0.5rem; /* Slightly smaller font */
  color: gray; /* Neutral text color */
  text-align: left; /* Center-align description */
  font-family: "Montserrat"; /* Consistent font */
}

/* Product price */
.vendorprofile-products-card-price {
  font-size: 0.1rem; /* Price font size */
  font-weight: "san-sarif"; /* Emphasize price */
  color: #000; /* Black text for price */
  text-align: left; /* Center-align price */
  font-family: "Montserrat"; /* Consistent font */
}

.vendorProducts-grid {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.vendorProducts-grid-item {
  display: flex;
  justify-content: flex-start;
}

.Vendorprofile-contact-button {
  width: 20%;
  height: 30px;
  border-radius: 30px;
  color: white;
  background-color: #000;
  font-family: "Montserrat";
}

.Vendorprofile-contact-button:hover {
  background-color: #000;
  border: #000;
}

.Vendorprofile-website-button:hover {
  background-color: #e5e5e5;
  border: #e5e5e5;
}

.Vendorprofile-website-button {
  font-family: "Montserrat";
}

/* checkout page */
/* Container for the checkout page */
/* .checkout-container {
  position: relative;
  width: 100vw;
  height: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  margin-top: -20px;
} */

/* Blurred background */
.checkout-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100vw;
  height: 100%;
  gap: 0.3px;
  padding-bottom: 40px;
  background-image: url("/Assets/Checkout/Checkout-background.webp"); /* Replace with your image path */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.checkout-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(10px); /* Adds blur to the background */
  /* background-color: rgba(
    0,
    0,
    0,
    0.4
  ); Adds a semi-transparent dark overlay */
  z-index: 1;
}

/* Form container */
.checkout-form {
  position: relative;
  width: 60%;
  height: 50%;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8); /* Semi-transparent white */
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  z-index: 2000;
  text-align: center;
  z-index: 1;
  margin-top: 1.2rem;
}

.checkout-form h2 {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: bold;
  text-align: left;
  font-family: "Horizon";
  font-weight: bold;
  padding-left: 1rem;
}

.checkout-form form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkout-form input {
  padding: 0.8rem;
  font-size: 1rem;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.form-navigation button {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 50%;
  background-color: #434343;
  border-radius: 8px;
  padding: 10px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin: auto;
  margin-top: 20px;
  letter-spacing: 5px;
  font-family: "Montserrat";
}

.form-navigation button:hover {
  background-color: #000;
}
.input-group input.error,
.input-group select.error,
.input-group textarea.error {
  border: 1px solid red !important;
  background-color: rgba(255, 0, 0, 0.05);
}

.error-message {
  color: red;
  font-size: 12px;
  margin-top: 4px;
  text-align: left;
}

.checkout_img_container {
  display: flex;
  justify-content: center;
}

.checkout_logo_icon {
  width: 15%;
  height: 50px;
  padding: 3rem;
}

.step-tracker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.step-item {
  display: flex;
  align-items: center;
  position: relative;
}

.step-circle {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid #ccc;
  background-color: white;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.step-item.active .step-circle {
  border-color: #000000;
  background-color: #fff;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.4);
}

.step-icon {
  font-size: 20px;
  width: 30px;
}

.step-line {
  width: 150px;
  height: 2px;
  background-color: #ccc;
  margin: 0 10px;
}

.step-item.active ~ .step-line {
  background-color: #000;
}

.Billinginfo_container {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  gap: 24px;
  align-items: center;
}

.Billinginfo_checkbox {
  width: 85%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 6rem;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.8); /* Semi-transparent white */
}

.billinginfo-form-container {
  display: flex;
  flex-direction: row;
  padding: 10px;
  width: 85%;
  justify-content: space-between;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8); /* Semi-transparent white */
}

.billinginfo-form {
  justify-content: flex-start;
}

.billinginfo-total {
  justify-content: flex-end;
}

.form-container {
  display: flex;
  flex-direction: column;
  width: 90%;
  padding: 20px;
  gap: 2rem;
}

.form-row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 15px;
}

.input-group {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.input-group input,
select {
  padding: 10px;
  border: 1px solid #000;
  border-radius: 6px;
  font-size: 14px;
  width: 100%;
}
.custom-phone-input .selected-flag {
  border-radius: 0px !important;
}
.react-tel-input .selected-flag {
  border-radius: 0px !important;
}

.react-tel-input .flag-dropdown {
  background-color: transparent !important;
  border-radius: 0px !important;
}

.phone-number-group {
  display: flex;
  gap: 10px;
}

.phone-number-group select,
.phone-number-group input {
  padding: 8px;
  border: 1px solid #000;
  border-radius: 4px;
  font-size: 14px;
}

.half-width {
  flex: 0.5;
  width: 6rem;
}

button {
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 20px;
}

button:hover {
  background-color: #0056b3;
}

.Billinginfo-total {
  background: #6c7c59;
  border-radius: 8px;
  padding: 16px;
  color: #fff;
  text-align: left;
  height: 10rem;
  margin-top: 1.5rem;
  width: 25%;
  margin-right: 1.5rem;
}

.cart-title {
  font-family: "Horizon";
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
  color: whitesmoke;
}

.cart-summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 16px;
  font-family: "Montserrat";
}

.cart-summary-total {
  display: flex;
  justify-content: space-between;
  font-family: "Montserrat";
  border-top: 1px solid #ccc;
  padding-top: 8px;
  margin-top: 8px;
  font-size: 18px;
}

.ShippingForm_container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5rem;
}

.shipping-form-container {
  align-items: center;
  justify-content: center;
  width: 85%;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8); /* Semi-transparent white */
}

.shippingform-form-container {
  display: flex;
  flex-direction: column;
  gap: 50px;
  justify-content: center;
  align-items: center;
  height: 20rem;
}

.shippingform-form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 100px;
  width: 80%;
}

.shippingform-form-row-zip {
  width: 30%;
  text-align: left;
  justify-content: flex-start;
  align-items: flex-start;
  margin-left: -26.5rem;
}

.input-group {
  flex: 1;
}
input {
  width: 80%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

input:focus {
  outline: none;
  border-color: #6c7c59;
  box-shadow: 0 0 4px rgba(108, 124, 89, 0.4);
}
.zip-code-input {
  width: 42.6% !important;
}

@media (max-width: 768px) {
  .zip-code-input {
    width: 100% !important;
  }
}
.Ordersummary-bigcontainer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
}

.Ordersummary-firstrow-firstcolumn {
  height: auto;
  font-family: "Montserrat";
  width: 80%;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-left: 1rem;
}

.product-header {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr;
  font-weight: 600;
  color: #555;
  background-color: #f9f9f9;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}
.product-content {
  padding: 10px 20px;
  max-height: 240px;
  overflow-y: auto;
}

.product-content::-webkit-scrollbar {
  width: 8px;
}

.product-content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 10px;
}

.product-content::-webkit-scrollbar-thumb {
  background: #6b7b58;
  border-radius: 10px;
}

.product-content::-webkit-scrollbar-thumb:hover {
  background: #4e5b44;
}
.product-row {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}
.product-row:last-child {
  border-bottom: none;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 70px;
  height: 70px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.unit-price,
.quantity {
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

.total-price {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}
.shipping-details {
  padding: 16px 20px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
}

.shipping-details ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  font-size: 13px;
  color: #666;
}

.shipping-details li {
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.shipping-details li:last-child {
  margin-bottom: 0;
}

/* Cart items in order summary */
.ordersummary-cart-items {
  max-height: 140px;
  overflow-y: auto;
  margin-bottom: 16px;
  border-radius: 8px;
}

.ordersummary-cart-item {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  border-bottom: 1px solid #ccc;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
}

.ordersummary-cart-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

/* Custom scrollbar for the cart items */
.ordersummary-cart-items::-webkit-scrollbar {
  background-color: #6c7b58;
  width: 8px;
}

.ordersummary-cart-items::-webkit-scrollbar-track {
  background: #2d2d2d;
  border-radius: 10px;
}

.ordersummary-cart-items::-webkit-scrollbar-thumb {
  background: #2d2d2d;
  border-radius: 10px;
}

.ordersummary-cart-items::-webkit-scrollbar-thumb:hover {
  background: #2d2d2d;
}

.ordersummary-cart-item-image {
  width: 100px;
  margin: 6px;
  flex-shrink: 0;
  margin-right: 10px;
  border-radius: 4px;
  background-color: transparent;
  object-fit: contain !important;
}

.ordersummary-cart-item-details {
  flex: 1;
  padding-right: 8px;
}

.ordersummary-cart-item-name {
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #2d2d2d;
}

.ordersummary-cart-item-quantity {
  color: #666;
  font-size: 14px;
}

.ordersummary-cart-item-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 100px;
  text-align: right;
  color: #2d2d2d;
}
.Ordersummary-firstrow {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
}

.ordersummary-total {
  background-color: #6c7c59;
  border-radius: 8px;
  padding: 16px;
  color: #fff;
  text-align: left;
  height: auto;
  margin-right: 1.5rem;
  margin-left: 1rem;
}

.ordersummary-cart-title {
  font-family: "Horizon";
  color: #eae3e4;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}

.ordersummary-cart-summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 16px;
  font-family: "montserrat";
}

.ordersummary-cart-summary-total {
  display: flex;
  justify-content: space-between;
  font-family: "montserrat";
  border-top: 1px solid #ccc;
  padding-top: 8px;
  margin-top: 8px;
  font-size: 18px;
}

.Ordersummary-secondrow {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
}

.Ordersummary-secondrow-firstcolumn,
.Ordersummary-secondrow-secondcolumn {
  display: flex;
  flex-direction: column;
  width: 50%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-left: 1rem;
  text-align: left;
}

.Ordersummary-secondrow-firstcolumn h1,
.Ordersummary-secondrow-secondcolumn h1 {
  font-size: 16px;
  margin-left: 1rem;
}

.Ordersummary-secondrow-firstcolumn p,
.Ordersummary-secondrow-secondcolumn p {
  font-size: 14px;
  margin-left: 1rem;
  font-family: "montserrat";
}

.Ordersummary-thirdrow {
  width: 95%;
  background: #fff;
  margin-left: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.Ordersummary-firstrow-secondcolumn-secondrow {
  width: 90%;
  margin-left: 1rem;
}

.paymentmethod-container {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
}

.paymentmethod-firstrow-firstcolumn {
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
}

/* Flex container for radio button and dropdown */
.paymentmethod-radio-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between; /* Align the dropdown to the right */
  gap: 10px;
  margin-bottom: 10px;
}

/* Dropdown menu styling */
.paymentmethod-dropdown select {
  width: 120px;
  height: 30px;
  font-family: "Montserrat";
  font-size: 0.9rem;
}

/* Fonts */
.horizon-font {
  font-family: "Horizon";
  font-weight: bold;
  font-size: 1rem;
}

.montserrat-font {
  font-family: "Montserrat";
  font-size: 0.9rem;
}

/* Card details layout */
.paymentmethod-card-details {
  margin-top: 10px;
}

.paymentmethod-card-details .MuiTextField-root {
  margin-top: 10px;
}

.paymentmethod-card-details button {
  margin-top: 15px;
  width: 100%;
  background-color: #434343;
  border-radius: 15px;
}

/* Separator between payment options */
.paymentmethod-option {
  position: relative;
  padding: 10px 0;
  border-bottom: 1px solid #ccc; /* Add the line below each payment option */
}

/* shooping cart overlay */
/* Overlay container */
.shopping-cart-overlay {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100%;
  background-color: #fff;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  z-index: 1200;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Header section */
.shopping-cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 10px;
}

.shopping-cart-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
}

.close-btn {
  background: none;

  border: none;
  font-size: 18px;
  cursor: pointer;
}

/* Cart items */
.cart-items {
  margin: 20px 0;
}

.cart-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.cart-item img {
  width: 60px;
  height: 60px;
  margin-right: 15px;
  border-radius: 5px;
}

.cart-item p {
  margin: 0;
  font-size: 16px;
}

.cart-item span {
  font-size: 14px;
  color: #888;
}

.remove-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #888;
  margin-left: auto;
  cursor: pointer;
}

/* Footer section */
.cart-footer {
  border-top: 1px solid #e0e0e0;
  padding-top: 10px;
}

.cart-footer p {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  font-weight: bold;
}

.cart-footer-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.cart-btn,
.checkout-btn {
  flex: 1;
  padding: 10px;
  margin: 0 5px;
  font-size: 14px;
  cursor: pointer;
  border: none;
  border-radius: 5px;
}

.cart-btn {
  background-color: #fff;
  border: 1px solid #000;
  color: #000;
}

.checkout-btn {
  background-color: #000;
  color: #fff;
}

/* cart popup */
/* .Cart-popup {
  position: "fixed";
  top: 0;
  right: 0;
  width: "500px";
  height: "100%";
  background-color: "white";
  box-shadow: "-2px 0 5px rgba(0,0,0,0.2)";
  display: "flex";
  flex-direction: "column";
  padding: "16px";
  z-index: 1000;
} */

.cart-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #ddd; /* Optional: Adds a subtle separator */
}

.close-cart-popup {
  cursor: pointer;
  font-size: 24px; /* Adjust size as needed */
  color: grey; /* Adjust color if needed */
  transition: color 0.2s ease-in-out;
}

.close-cart-popup:hover {
  color: black; /* Change color on hover */
}

.Cart-popup h6 {
  font-size: 18px;
  font-family: "horizon";
  font-weight: bold;
}

.Cart-popup button {
  background-color: #2e2e2d;
}

/* menu drop */

.menu-overlay {
  position: absolute;
  top: 100px;
  right: 80;
  width: 60%;
  height: 170%;
  background-color: #fff;
  background-color: rgba(
    255,
    255,
    255,
    0.93
  ); /* Slightly transparent background */
  backdrop-filter: blur(10px); /* Apply blur effect to the background */
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  z-index: 5000;
  overflow-y: hidden;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-radius: 20px;
  gap: 200px;
  pointer-events: auto; /* Ensure it captures mouse events */
}

.menu-left-container {
  display: flex;
  flex-direction: column;
  width: 50%;
  height: 100%;
  padding: 0.8rem;
  background-color: #ffffff;
}

.topcategory-title {
  font-family: Horizon;
  font-weight: bold;
  font-size: 0.9rem;
  text-align: left;
}

.menu-left {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 10px;
}

.menu-shopall {
  font-size: 0.6rem;
  margin-bottom: -2px;
  margin-top: -2px;
  border-bottom: 1px solid black;
}

.menu-item {
  cursor: pointer;
  font-weight: bold;
  height: 3px;
}

.menu-item:active {
  height: 100%;
  background-color: #ccc;
}

.menu-item:last-child {
  border-bottom: none; /* Removes the line from the last item */
}

.menu-item h3 {
  font-family: montserrat;
  margin-top: -5px;
  font-size: 0.8rem;
}

.menu-right-container {
  display: flex;
  flex-direction: row;
  width: 60%;
  margin-left: -150px;
}

.menu-right {
  width: 54%;
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: left;
}

.menu-right h2 {
  font-family: Horizon;
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 0.8rem;
  margin-left: -21px;
  margin-top: 17px;
  text-align: left;
}

.menu-right ul {
  list-style: none;
  padding: 0;
  margin-left: -20px;
  margin-top: -5px;
  font-family: "montserrat";
  font-size: 0.6rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 5px;
  align-items: flex-start;
}

.menu-right ul li {
  font-family: "montserrat";

  gap: 60px;
  font-size: 0.7rem;
}

.menu-right img {
  margin-top: 10px;
  width: 100%;
  border-radius: 8px;
}

/* Parent container for positioning */
.progress-container-hero {
  position: absolute;
  bottom: 9px;
  left: 50%;
  transform: translateX(-50%);
  width: 5%; /* Adjust the width of the progress bar */
  z-index: 3000;
  border-radius: 20px;
  height: 10px;
  background-color: rgba(
    0,
    0,
    0,
    0.5
  ); /* Semi-transparent background to see the container */
  border: 1px solid rgb(0, 0, 0); /* Debugging border to ensure visibility */
  height: 4px; /* Thin progress bar */
  background-color: #363636; /* Grey background */
  border-radius: 20px;
}
/* Custom progress bar styling */
.custom-progress-bar-hero {
  width: 100%; /* Full width */
  max-width: 800px; /* Adjust for a specific maximum width */
  height: 8px; /* Slightly thicker progress bar */
  background-color: #ffffff13; /* Grey background */
  border-radius: 20px; /* Rounded edges */
  overflow: hidden; /* Prevent overflow for the bar */
  position: relative; /* For absolute positioning of the blur effect */
}

.custom-progress-bar-hero .MuiLinearProgress-bar {
  background-color: #6c6c6c; /* Darker grey for the progress */
  height: 100%; /* Full height */
  filter: blur(2px); /* Add a blur effect */
  transition: all 0.3s ease; /* Smooth animation for changes */
}

.custom-progress-bar-hero:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0)
  );
  opacity: 0.3; /* Subtle overlay */
  pointer-events: none; /* Ignore interactions */
}
/*Request Info Popup*/
/* RequestInfoPopup.css */

.request-popup-dialog {
  z-index: 50000;
  backdrop-filter: blur(14px);
}
.Request-Info-close-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  color: #2d2d2d;
}
.request-popup-paper {
  width: 65%;
  height: 80%;
  border-radius: 20px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  background-color: #faf9f6;
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.1);
}

.request-popup-close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  color: #2d2d2d;
  text-align: right;
}

.request-popup-title h2 {
  margin: 23px;
  text-align: left;
  font-size: 30px;
  font-weight: bold;
  font-family: "Horizon";
  color: #2d2d2d;
}

.request-popup-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0;

  margin-top: 0;
}

.request-popup-button {
  font-size: 24px;
  font-weight: bold;
  background-color: transparent;
  color: #2d2d2d;
  width: 40%;
  height: 104px;
  border: 1px solid #2d2d2d;
  border-radius: 16px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease;
}

.request-popup-button:hover {
  background-color: #2d2d2d;
  color: #ffffff;
}
/* Floating button */
.floating-button {
  position: fixed;
  bottom: 16px;
  right: 16px;
  background-color: #5f7c5f;
  color: white;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.floating-button:hover {
  background-color: #4e644e;
}

/* Chat Popup */
.chat-popup {
  position: fixed;
  bottom: 80px;
  right: 16px;
  width: 300px;
  height: 400px;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  overflow: hidden;
}

/* Chat Header */
.chat-header {
  background-color: #5f7c5f;
  color: white;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.close-icon {
  cursor: pointer;
}

/* Chat Body */
.chat-body {
  flex: 1;
  padding: 10px;
  background-color: #f5f5f5;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message {
  max-width: 70%;
  padding: 10px;
  border-radius: 10px;
  font-size: 14px;
  line-height: 1.4;
}

/* Received Message */
.message.received {
  background-color: #e0e0e0;
  align-self: flex-start;
}

/* Sent Message */
.message.sent {
  background-color: #5f7c5f;
  color: white;
  align-self: flex-end;
}

/* Chat Footer */
.chat-footer {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ddd;
  gap: 8px;
}

.message-input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 20px;
  font-size: 14px;
}

.send-button {
  padding: 8px 16px;
  background-color: #5f7c5f;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.send-button:hover {
  background-color: #4e644e;
}

/* subcategories page */
.subcategory-container {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.subcategory-box {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: 0.3s ease-in-out;
}

/* .subcategory-box:hover {
  transform: scale(1.02);
} */

.subcategory-box.reverse {
  flex-direction: row-reverse;
}

.subcategory-image {
  width: 50%;
  height: 100%;
  object-fit: cover;
}
.subcategory-image-container {
  width: 50%;
  height: 100%;
  overflow: hidden;
}
.subcategory-content {
  width: 50%;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}
/* Apply reverse layout to every other box */
.subcategory-box.reverse {
  flex-direction: row-reverse;
}

.subcategory-content h3 {
  font-size: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  font-family: "Horizon";
}

.subcategory-content h2 {
  font-size: 16px;
  color: #555;
  margin-bottom: 20px;
  font-family: "Montserrat";
  text-align: justify;
  padding: 0px 15px;
}

.subcategory-button {
  justify-content: center;
  background: black;
  color: white;
  padding: 12px 18px;
  text-decoration: none;
  text-align: center;
  width: 160px;
  border-radius: 6px;
  transition: 0.3s;
  align-self: center;
}

.subcategory-button:hover {
  background: #333;
  transform: scale(1.02);
}

/* 

.full-page-menu.open {
  left: 0; 
  opacity: 1; 
}
.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); 
  z-index: 999; 
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none; 
}

.backdrop.open {
  opacity: 1;
  pointer-events: auto; 
}
.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.menu-logo {
  width: 120px; 
  height: auto;
}

.close-button {
  color: #2d2d2d;
}

.menu-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  margin-top: 50px; 
}

.menu-item {
  font-size: 24px;
  font-weight: 500;
  font-family: "Montserrat";
  color: #2d2d2d;
  cursor: pointer;
  text-transform: uppercase;
  text-decoration: none;
  transition: transform 0.2s ease-in-out;
}

.menu-item:hover {
  transform: scale(1.1);
  color: #2d2d2d;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-item {
  opacity: 0;
  transform: translateY(-20px);
  animation: slideIn 0.5s ease forwards;
}

.menu-item:nth-child(1) {
  animation-delay: 0.1s;
}

.menu-item:nth-child(2) {
  animation-delay: 0.2s;
}

.menu-item:nth-child(3) {
  animation-delay: 0.3s;
}

.menu-item:nth-child(4) {
  animation-delay: 0.4s;
}
@keyframes dropIn {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-categories {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 5px;
}

.menu-category-item {
  font-size: 20px;
  font-weight: 500;
  opacity: 0; 
  transform: translateY(-30px);
  animation: dropIn 0.5s ease forwards;
}

.menu-category-item:nth-child(1) {
  animation-delay: 0.1s;
}

.menu-category-item:nth-child(2) {
  animation-delay: 0.2s;
}

.menu-category-item:nth-child(3) {
  animation-delay: 0.3s;
}

.menu-category-item:nth-child(4) {
  animation-delay: 0.4s;
}

.menu-category-item:nth-child(5) {
  animation-delay: 0.5s;
} */
.full-page-menu {
  position: fixed;
  font-family: "Montserrat";
  top: 0;
  left: -100%;
  width: 100%;
  height: 100vh;
  background-color: white;
  z-index: 10000;
  opacity: 0;
  overflow-y: auto; /* In case the menu content is longer */
  transform: translateX(-100%);
  transition: transform 0.5s ease, opacity 0.5s ease;
}
.full-page-menu.open {
  left: 0; /* Slide in to the left */
  transform: translateX(0);
  opacity: 1;
}

.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */
  z-index: 999; /* Below the menu */
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none; /* Prevent clicks when hidden */
}

.backdrop.open {
  opacity: 1;
  pointer-events: auto; /* Allow clicks when visible */
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.menu-logo {
  width: 120px; /* Adjust the size as needed */
  height: auto;
}

.close-button {
  color: #2d2d2d;
}

.menu-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  margin-top: 50px; /* Add space below the logo */
}

.menu-item {
  font-size: 24px;
  font-weight: 500;
  font-family: "Montserrat";
  color: #2d2d2d;
  cursor: pointer;
  text-transform: uppercase;
  text-decoration: none;
  /* Remove animation to prevent the sliding effect */
  opacity: 1; /* Ensure they are visible */
  transform: none; /* Prevent any transformation */
  transition: transform 0.2s ease-in-out;
}

.menu-item:hover {
  transform: scale(1.1);
  color: #2d2d2d;
}

.menu-categories {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 1rem;
  padding: 1rem;
  align-items: center;
  border-radius: 5px;
}

.menu-category-item {
  font-size: 20px;
  font-weight: 500;
  opacity: 1; /* Ensure they are visible */
  transform: none; /* Prevent any transformation */
  /* Remove animation to prevent the dropping effect */
}

/* Adjust based on how many categories you have */
/*Footer*/

/* footer.css */

/* Main Footer */
.footer-container {
  background-color: #f7f2f2;
  padding: 5rem;
  padding-bottom: 1rem;
  margin-top: 2rem;
  padding-top: 5rem;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  overflow: hidden;
}

/* Footer Layout */
.footer-layout {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: 3rem;
}

.footer-layout-md {
  flex-direction: row;
  align-items: center;
  gap: 5rem;
}

/* Logo Section */
.footer-logo-container {
  flex: 1;
}

.footer-logo {
  margin-bottom: 2rem;
}

/* Subscribe Section */
.footer-subscribe-container {
  position: relative;
  width: 100%;
}

.footer-subscribe-form {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.footer-subscribe-button {
  text-transform: none;
  font-size: 14px;
  width: 120px;
  height: 30px;
  margin-bottom: 1rem;
  background-color: #2d2d2d;
}

.footer-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.42);
}

/* Footer Links */
.footer-links-container {
  flex: 2;
  min-width: 300px;
}

.footer-link-title {
  font-weight: bold;
  color: #6b7b58;
  margin-bottom: 2rem;
  font-size: 12px;
  font-family: "Horizon", Bold;
}

.footer-link {
  text-decoration: none;
  color: #2d2d2d;
}

.footer-link-section {
  margin-top: 4rem;
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 4rem;
  border-top: 1px solid black;
  padding-top: 2rem;
  width: 100%;
}

.footer-bottom-md {
  flex-direction: row;
  align-items: center;
  padding-top: 4rem;
}
.footer-bottom-left-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.footer-socials {
  display: flex;
  gap: 1rem;
}

.footer-contact-info {
  font-family: "Montserrat";
}

.footer-payment-logos {
  display: flex;
  gap: 1rem;
  padding-top: 0.5rem;
}

.footer-cookie-policy {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 1rem;
  padding-top: 1rem;
}

.footer-small-text {
  font-family: "Montserrat";
  font-size: 5px;
  display: flex;
  flex-direction: row;
}

.footer-bottom-lock {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.footer-lock-icon {
  font-size: small;
  padding-top: 0.5rem;
  text-align: right;
}

.footer-text {
  font-family: "Montserrat";
  font-size: 12px;
}

.error-message {
  color: red;
  font-size: 12px;
  font-family: "Montserrat";
  margin-top: 3px;
}

.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7); /* Optional, adds a dark overlay */
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading-screen.fade-out {
  animation: fadeOut 0.5s ease-in-out forwards;
}
.fade-and-scale {
  opacity: 0;
  transform: scale(0.95); /* Slightly zoom out */
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.video-progress-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -30px;
  gap: 10px;
}

.video-progress-dot {
  width: 30px;
  height: 6px;
  background-color: #2d2d2d;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Progress bar when video is playing */
.video-progress-dot.active {
  background-color: rgba(0, 0, 0, 0.1);
  scale: 1.1;
}

/* Circle style for inactive dots */
.video-progress-dot.circle {
  width: 12px;
  height: 12px;
  background-color: #6c7b58;
  border-radius: 50%;
}

/* Progress bar inside active dot */
.video-progress-bar {
  height: 100%;
  background-color: black;
  transition: width 0.2s linear;
}
/*View in store*/
.request-viewInStore-popup-dialog {
  width: "80%";
  border-radius: 20px;
  padding: 20px;
  position: relative;
  overflow: visible;
  background-color: #faf9f6;
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.1);
}

/* types page */

.build-package-section {
  height: auto;
  padding: 5% 8%;
}
.invoice-download-btn {
  margin-top: 10px;
  background-color: #2d2d2d !important;
  color: #fff !important;
  border-radius: 5px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: 0.3s ease-in-out;
  font-size: 16px;
}

.invoice-download-btn:hover {
  background-color: #ddd;
  color: #2d2d2d;
}
.invoice-modal {
  position: fixed;
  top: 10%;
  left: 50%;
  transform: translate(-50%, 0);
  background: white;
  padding: 20px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  border-radius: 8px;
}

.invoice-modal button {
  margin-top: 10px;
  padding: 8px 12px;
  background: #007bff;
  color: white;
  border: none;
  cursor: pointer;
}
/* Modal Overlay */
.greeting-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Modal Box */
.greeting-modal {
  display: flex;
  width: 800px;
  background: #f7f4f1;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

/* Left Side (Image) */
.greeting-left {
  width: 50%;
}

.greeting-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Right Side (Content) */
.greeting-right {
  width: 50%;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center; /* Centers the text */
  justify-content: center; /* Centers vertically */
  text-align: center; /* Centers text inside */
  padding: 20px;
  position: relative;
}

/* Close Button */
.greeting-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #2d2d2d;
}

.greeting-close:hover {
  color: #2d2d2d;
  background-color: transparent;
}

/* Check Icon */
.greeting-check {
  width: 40px;
  margin-bottom: 10px;
}

/* Title */
.greeting-title {
  font-size: 24px;
  font-weight: bold;
  color: #2d2d2d;
  margin-bottom: 10px;
}

/* Description */
.greeting-text {
  font-size: 16px;
  color: #2d2d2d;
}

/* Toast Styles */
.toast-container {
  position: fixed;
  top: 50px;
  right: 20px;
  z-index: 1000;
}

.toast-content {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease-out;
}

.toast-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-close:hover {
  opacity: 0.8;
}

/* Accounting Page */
.accounting-page {
  padding: 20px;
}

.dashboard-header-vendor {
  margin-bottom: 20px;
}

.accounting-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.graph-container {
  display: flex;
  gap: 20px;
}

.graph {
  flex: 1;
  height: 200px;
  background-color: #f0f0f0;
  overflow: hidden;
}

.report-options {
  gap: 20px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
/* Payment iframe styling */
.payment-iframe-container {
  width: 100%;
  margin-top: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.payment-iframe-container iframe {
  width: 100%;
  height: 600px;
  border: none;
  display: block;
  background-color: white;
}

/* Fix for iframe visibility issues */
.paymentmethod-container {
  position: relative;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 10px;
  overflow: visible;
}

/* Ensure the iframe has higher z-index than other elements */
iframe[title="Paymob Payment"] {
  position: relative;
  z-index: 1001;
  background-color: white;
}

/* --- Search Suggestion Dropdown Large Restyle --- */
.suggestion-item-large {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 18px 20px;
  gap: 24px;
  cursor: pointer;
  border-bottom: 1px solid #e0e0e0;
  background: #fff;
  transition: background 0.18s;
}
.suggestion-item-large:last-child {
  border-bottom: none;
}
.suggestion-item-large:hover {
  background: #f5f5f5;
}
.suggestion-image-large-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90px;
  height: 90px;
  border-radius: 12px;
  background: #f2f2f2;
  overflow: hidden;
}
.suggestion-image-large {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 10px;
  background: #f2f2f2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}
.suggestion-image-placeholder-large {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.suggestion-info-large {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 0;
}
.suggestion-name-large {
  font-size: 1.15rem;
  font-weight: 700;
  color: #2d2d2d;
  font-family: "Montserrat", "Horizon", sans-serif;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.suggestion-meta-large {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 6px;
  font-size: 0.98rem;
  color: #888;
  font-family: "Montserrat", sans-serif;
}
.suggestion-category-large {
  color: #6b7b58;
  font-weight: 500;
  font-size: 0.98rem;
}
.suggestion-brand-large {
  color: #888;
  font-size: 0.98rem;
}
.suggestion-price-row-large {
  display: flex;
  align-items: baseline;
  gap: 10px;
  margin-top: 2px;
}
.suggestion-old-price-large {
  text-decoration: line-through;
  color: #bbb;
  font-size: 1.05rem;
  font-weight: 400;
}
.suggestion-sale-price-large {
  color: #d32f2f;
  font-size: 1.15rem;
  font-weight: 700;
}
.suggestion-regular-price-large {
  color: #2d2d2d;
  font-size: 1.15rem;
  font-weight: 700;
}

@media (max-width: 600px) {
  .suggestion-item-large {
    padding: 10px 8px;
    gap: 10px;
  }
  .suggestion-image-large-container {
    width: 60px;
    height: 60px;
  }
  .suggestion-image-large,
  .suggestion-image-placeholder-large {
    width: 50px;
    height: 50px;
  }
  .suggestion-name-large {
    font-size: 1rem;
  }
  .suggestion-meta-large,
  .suggestion-category-large,
  .suggestion-brand-large {
    font-size: 0.85rem;
  }
  .suggestion-old-price-large,
  .suggestion-sale-price-large,
  .suggestion-regular-price-large {
    font-size: 1rem;
  }
}
/* --- End Search Suggestion Dropdown Large Restyle --- */
