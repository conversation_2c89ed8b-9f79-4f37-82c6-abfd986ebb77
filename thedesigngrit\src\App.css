@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap");
@font-face {
  font-family: "Horizon";
  src: url("../public/fonts/horizon/horizon.otf") format("opentype");
  font-weight: normal;
  font-style: normal;
}
.App {
  text-align: center;
  font-family: "Horizon", "Montserrat";
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Media Queries */
@media (max-width: 1200px) {
  .partners-section {
    gap: 2rem;
  }

  .partners-heading {
    font-size: 2rem;
  }

  .partners-logo-grid {
    gap: 1.5rem;
  }
}

@media (max-width: 968px) {
  .partners-section {
    flex-direction: column;
    gap: 3rem;
  }

  .partners-content {
    flex: 0 1 auto;
    width: 100%;
    text-align: center;
  }

  .partners-logo-grid {
    flex: 0 1 auto;
    width: 100%;
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 640px) {
  .partners-logo-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .partners-heading {
    font-size: 1.75rem;
  }
}

@media (min-width: 768px) {
  .product-card {
    min-width: 25%;
  }
}

@media (max-width: 767px) {
  .product-card {
    min-width: 50%;
  }
}

@media (max-width: 480px) {
  .product-card {
    min-width: 100%;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-job-container {
    flex-direction: column;
    height: auto;
    padding: 30px;
  }

  .description-content-1 h2,
  .description-content-1 p {
    padding-left: 40px;
    padding-right: 40px;
  }
}

@media (max-width: 768px) {
  .hero-job-container {
    text-align: center;
    gap: 20px;
  }

  .Description {
    padding: 30px;
  }

  .description-content-1 h2 {
    padding-left: 0;
    text-align: center;
  }

  .description-content-1 p {
    padding: 0 20px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .hero-job-container {
    padding: 20px;
    height: auto;
    flex-direction: column;
  }

  .Description {
    padding: 20px;
  }

  .description-content-1 {
    padding: 10px;
  }

  .description-content-1 h2 {
    text-align: center;
    padding: 0;
  }

  .description-content-1 p {
    padding: 0 10px;
    font-size: 14px;
    line-height: 180%;
  }
}

@media (max-width: 600px) {
  .job-form-card {
    padding: 1.5rem;
  }

  .job-form-row {
    flex-direction: column;
    gap: 2rem;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .terms-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #ddd;
  }

  .content {
    width: 100%;
    padding: 20px;
  }
}

/* Media Queries for Responsiveness */

/* Tablet Screens */
@media (max-width: 1024px) {
  .content-container {
    flex-direction: column;
    padding: 20px 50px;
  }

  .Contant-caption {
    margin: 20px 0;
    padding: 20px;
    text-align: center;
  }

  .Contact-form {
    width: 100%;
    max-width: 600px;
    margin: 20px auto;
  }
  .Contact-Page {
    position: relative;
    width: 100%;
    height: 150vh; /* Full viewport height */
    display: flex;
    flex-direction: column;
  }
}

/* Mobile Screens */
@media (max-width: 768px) {
  .content-container {
    padding: 20px;
  }

  .Contant-caption h3 {
    font-size: 1.5rem;
  }

  .Contant-caption p {
    font-size: 1rem;
  }

  .Contact-form {
    width: 100%;
    max-width: 100%;
    padding: 15px;
  }

  .contact-form-field input,
  .contact-form-field textarea {
    padding: 0.5rem;
  }

  .contact-submit-btn {
    padding: 0.5rem;
    font-size: 12px;
  }
}

/* Animation for sending the mail */
@keyframes mailSend {
  0% {
    transform: scale(1) translateY(0) translateX(0);
    opacity: 1;
  }
  50% {
    transform: scale(1.2) translateY(-30px) translateX(50px);
  }
  100% {
    transform: scale(0) translateY(-60px) translateX(120px);
    opacity: 0;
  }
}

/* Popup icon styling */
.Job-sent-popup-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

/* Animation for the popup */
@keyframes slide-down {
  from {
    transform: translateY(-20%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
